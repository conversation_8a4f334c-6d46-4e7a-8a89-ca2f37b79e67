import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  LayoutDashboard, 
  Edit, 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertCircle,
  FileTemplate,
  Settings,
  BarChart3,
  LogOut,
  Menu,
  X,
  User,
  Calculator
} from "lucide-react";
import { useContractAuth } from "@/contexts/ContractAuthContext";
import { cn } from "@/lib/utils";

interface ContractSidebarProps {
  className?: string;
}

const ContractSidebar = ({ className }: ContractSidebarProps) => {
  const { user, contracts, logout } = useContractAuth();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const contractStats = {
    active: contracts.filter(c => c.status === 'sent').length,
    draft: contracts.filter(c => c.status === 'draft').length,
    signed: contracts.filter(c => c.status === 'signed').length,
    expired: contracts.filter(c => c.status === 'expired' || c.status === 'cancelled').length,
  };

  const navigationItems = [
    {
      title: "Dashboard",
      href: "/contract-dashboard",
      icon: LayoutDashboard,
      count: null
    },
    {
      title: "Active Contracts",
      href: "/contract-dashboard?filter=sent",
      icon: Clock,
      count: contractStats.active,
      color: "text-blue-600"
    },
    {
      title: "Draft Contracts",
      href: "/contract-dashboard?filter=draft",
      icon: Edit,
      count: contractStats.draft,
      color: "text-gray-600"
    },
    {
      title: "Signed Contracts",
      href: "/contract-dashboard?filter=signed",
      icon: CheckCircle,
      count: contractStats.signed,
      color: "text-green-600"
    },
    {
      title: "Expired/Cancelled",
      href: "/contract-dashboard?filter=expired",
      icon: XCircle,
      count: contractStats.expired,
      color: "text-red-600"
    },
    {
      title: "Templates",
      href: "/contract-templates",
      icon: FileTemplate,
      count: null
    },
    {
      title: "Analytics",
      href: "/contract-analytics",
      icon: BarChart3,
      count: null
    },
    {
      title: "Settings",
      href: "/contract-settings",
      icon: Settings,
      count: null
    }
  ];

  const isActive = (href: string) => {
    if (href === "/contract-dashboard") {
      return location.pathname === "/contract-dashboard" && !location.search;
    }
    return location.pathname + location.search === href;
  };

  return (
    <div className={cn(
      "flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300",
      isCollapsed ? "w-16" : "w-64",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-primary">Contract Builder</h2>
              <p className="text-xs text-muted-foreground">Professional Edition</p>
            </div>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 p-0"
        >
          {isCollapsed ? <Menu className="h-4 w-4" /> : <X className="h-4 w-4" />}
        </Button>
      </div>

      {/* User Profile */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="h-5 w-5 text-primary" />
          </div>
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">{user?.name}</p>
              <p className="text-xs text-muted-foreground truncate">{user?.email}</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);
          
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                active 
                  ? "bg-primary text-white" 
                  : "text-gray-700 hover:bg-gray-100",
                isCollapsed && "justify-center"
              )}
            >
              <Icon className={cn("h-5 w-5", item.color && !active ? item.color : "")} />
              {!isCollapsed && (
                <>
                  <span className="flex-1">{item.title}</span>
                  {item.count !== null && item.count > 0 && (
                    <Badge 
                      variant={active ? "secondary" : "outline"} 
                      className="text-xs"
                    >
                      {item.count}
                    </Badge>
                  )}
                </>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Quick Actions */}
      {!isCollapsed && (
        <div className="p-4 border-t space-y-2">
          <Button asChild className="w-full" size="sm">
            <Link to="/contracts" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>New Contract</span>
            </Link>
          </Button>
          <Button asChild variant="outline" className="w-full" size="sm">
            <Link to="/quotation-dashboard" className="flex items-center space-x-2">
              <Calculator className="h-4 w-4" />
              <span>Quotation Builder</span>
            </Link>
          </Button>
        </div>
      )}

      {/* Footer */}
      <div className="p-4 border-t">
        <Button
          variant="ghost"
          onClick={logout}
          className={cn(
            "w-full flex items-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50",
            isCollapsed && "justify-center"
          )}
          size="sm"
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span>Sign Out</span>}
        </Button>
      </div>
    </div>
  );
};

export default ContractSidebar;
