
import { But<PERSON> } from "@/components/ui/button";
import { Settings, Users, Calendar, FileText, Search, File, Menu, X, Clock, CheckSquare } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useState } from "react";

const Navigation = () => {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  const isActive = (path: string) => location.pathname === path;
  
  const navigationItems = [
    { path: "/dashboard", label: "Dashboard", icon: Settings },
    { path: "/costs", label: "Cost Tracking", icon: Calendar },
    { path: "/materials", label: "Materials", icon: FileText },
    { path: "/labor", label: "Labor", icon: Users },
    { path: "/progress", label: "Progress", icon: Clock },
    { path: "/tasks", label: "Tasks", icon: CheckSquare },
    { path: "/contracts", label: "Contracts", icon: File },
  ];
  
  return (
    <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 construction-gradient rounded-lg flex items-center justify-center">
              <Settings className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              ConstructionSync
            </h1>
          </Link>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navigationItems.map((item) => (
              <Button 
                key={item.path}
                variant={isActive(item.path) ? "default" : "ghost"} 
                asChild
                size="sm"
              >
                <Link to={item.path}>
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.label}
                </Link>
              </Button>
            ))}
          </nav>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" asChild className="hidden sm:flex">
              <Link to="/client/demo-token">
                <Search className="mr-2 h-4 w-4" />
                Client Portal
              </Link>
            </Button>
            
            {/* Mobile Menu Button */}
            <Button 
              variant="outline" 
              size="sm" 
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>
        
        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden mt-4 pb-4 border-t">
            <nav className="flex flex-col space-y-2 mt-4">
              {navigationItems.map((item) => (
                <Button 
                  key={item.path}
                  variant={isActive(item.path) ? "default" : "ghost"} 
                  asChild
                  className="justify-start"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Link to={item.path}>
                    <item.icon className="mr-2 h-4 w-4" />
                    {item.label}
                  </Link>
                </Button>
              ))}
              <Button variant="outline" asChild className="justify-start">
                <Link to="/client/demo-token" onClick={() => setMobileMenuOpen(false)}>
                  <Search className="mr-2 h-4 w-4" />
                  Client Portal
                </Link>
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Navigation;
