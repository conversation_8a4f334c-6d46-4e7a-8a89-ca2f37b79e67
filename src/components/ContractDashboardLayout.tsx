import { ReactNode, useState } from "react";
import ContractSidebar from "./ContractSidebar";
import { cn } from "@/lib/utils";

interface ContractDashboardLayoutProps {
  children: ReactNode;
  className?: string;
}

const ContractDashboardLayout = ({ children, className }: ContractDashboardLayoutProps) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <ContractSidebar className="fixed left-0 top-0 h-full z-40" />
      
      {/* Main Content */}
      <div className={cn(
        "flex-1 transition-all duration-300",
        sidebarCollapsed ? "ml-16" : "ml-64"
      )}>
        <main className={cn("min-h-screen", className)}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default ContractDashboardLayout;
