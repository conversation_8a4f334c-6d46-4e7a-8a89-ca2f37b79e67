import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  company: string;
  role: 'contractor' | 'client' | 'admin';
  avatar?: string;
  createdAt: string;
  lastLogin: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (userData: RegisterData) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  company: string;
  role: 'contractor' | 'client';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Mock users database (in real app, this would be API calls)
  const mockUsers: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Smith',
      company: 'Smith Construction',
      role: 'contractor',
      createdAt: '2025-01-01',
      lastLogin: '2025-01-15'
    },
    {
      id: '2',
      email: '<EMAIL>',
      firstName: 'Sarah',
      lastName: 'Johnson',
      company: 'Johnson Enterprises',
      role: 'client',
      createdAt: '2025-01-05',
      lastLogin: '2025-01-14'
    }
  ];

  useEffect(() => {
    // Check for stored authentication on app load
    const storedUser = localStorage.getItem('constructionSync_user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        localStorage.removeItem('constructionSync_user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock authentication logic
    const foundUser = mockUsers.find(u => u.email === email);
    
    if (!foundUser) {
      setIsLoading(false);
      return { success: false, error: 'User not found' };
    }
    
    // In real app, password would be properly validated
    if (password.length < 6) {
      setIsLoading(false);
      return { success: false, error: 'Invalid password' };
    }
    
    const userWithLastLogin = {
      ...foundUser,
      lastLogin: new Date().toISOString().split('T')[0]
    };
    
    setUser(userWithLastLogin);
    localStorage.setItem('constructionSync_user', JSON.stringify(userWithLastLogin));
    setIsLoading(false);
    
    return { success: true };
  };

  const register = async (userData: RegisterData): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === userData.email);
    if (existingUser) {
      setIsLoading(false);
      return { success: false, error: 'User with this email already exists' };
    }
    
    // Create new user
    const newUser: User = {
      id: Date.now().toString(),
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      company: userData.company,
      role: userData.role,
      createdAt: new Date().toISOString().split('T')[0],
      lastLogin: new Date().toISOString().split('T')[0]
    };
    
    // In real app, this would be saved to database
    mockUsers.push(newUser);
    
    setUser(newUser);
    localStorage.setItem('constructionSync_user', JSON.stringify(newUser));
    setIsLoading(false);
    
    return { success: true };
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('constructionSync_user');
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('constructionSync_user', JSON.stringify(updatedUser));
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
