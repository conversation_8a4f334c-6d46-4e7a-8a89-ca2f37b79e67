import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface QuotationUser {
  id: string;
  email: string;
  name: string;
  company?: string;
  phone?: string;
  createdAt: string;
}

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  total: number;
}

interface Quotation {
  id: string;
  title: string;
  type: string;
  status: 'draft' | 'sent' | 'approved' | 'rejected' | 'expired';
  clientName: string;
  clientEmail: string;
  subtotal: number;
  tax: number;
  total: number;
  createdDate: string;
  lastModified: string;
  approvedDate?: string;
  expiryDate?: string;
  template: string;
  lineItems: LineItem[];
  notes?: string;
  validUntil?: string;
}

interface QuotationAuthContextType {
  user: QuotationUser | null;
  quotations: Quotation[];
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, name: string, company?: string) => Promise<boolean>;
  logout: () => void;
  createQuotation: (quotationData: Omit<Quotation, 'id' | 'createdDate' | 'lastModified' | 'status'>) => Quotation;
  updateQuotation: (id: string, updates: Partial<Quotation>) => void;
  deleteQuotation: (id: string) => void;
  getQuotation: (id: string) => Quotation | undefined;
  resendQuotation: (id: string) => Promise<boolean>;
  duplicateQuotation: (id: string) => Quotation | null;
  convertToContract: (id: string) => boolean;
}

const QuotationAuthContext = createContext<QuotationAuthContextType | undefined>(undefined);

export const useQuotationAuth = () => {
  const context = useContext(QuotationAuthContext);
  if (context === undefined) {
    throw new Error('useQuotationAuth must be used within a QuotationAuthProvider');
  }
  return context;
};

interface QuotationAuthProviderProps {
  children: ReactNode;
}

export const QuotationAuthProvider: React.FC<QuotationAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<QuotationUser | null>(null);
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Mock data for demonstration
  const mockQuotations: Quotation[] = [
    {
      id: "QUO001",
      title: "Electrical Installation Quote",
      type: "electrical",
      status: "approved",
      clientName: "Tech Startup Inc",
      clientEmail: "<EMAIL>",
      subtotal: 15000,
      tax: 1500,
      total: 16500,
      createdDate: "2024-11-08",
      lastModified: "2024-11-10",
      approvedDate: "2024-11-10",
      template: "electrical-quote",
      lineItems: [
        {
          id: "1",
          description: "Main electrical panel upgrade",
          quantity: 1,
          unit: "unit",
          unitPrice: 3500,
          total: 3500
        },
        {
          id: "2", 
          description: "Office outlet installation",
          quantity: 20,
          unit: "each",
          unitPrice: 150,
          total: 3000
        },
        {
          id: "3",
          description: "LED lighting fixtures",
          quantity: 15,
          unit: "each",
          unitPrice: 200,
          total: 3000
        },
        {
          id: "4",
          description: "Electrical labor",
          quantity: 35,
          unit: "hour",
          unitPrice: 150,
          total: 5250
        }
      ],
      notes: "All materials included. Work to be completed during business hours.",
      validUntil: "2024-12-08"
    },
    {
      id: "QUO002",
      title: "Kitchen Materials Supply Quote",
      type: "materials",
      status: "sent",
      clientName: "Home Renovation Co",
      clientEmail: "<EMAIL>",
      subtotal: 8500,
      tax: 850,
      total: 9350,
      createdDate: "2024-11-12",
      lastModified: "2024-11-12",
      template: "material-supply",
      lineItems: [
        {
          id: "1",
          description: "Premium granite countertops",
          quantity: 45,
          unit: "sqft",
          unitPrice: 120,
          total: 5400
        },
        {
          id: "2",
          description: "Custom kitchen cabinets",
          quantity: 12,
          unit: "unit",
          unitPrice: 250,
          total: 3000
        }
      ],
      notes: "Delivery within 2 weeks of order confirmation.",
      validUntil: "2024-12-12"
    }
  ];

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem('quotationUser');
    const savedQuotations = localStorage.getItem('quotationUserQuotations');
    
    if (savedUser) {
      setUser(JSON.parse(savedUser));
      setQuotations(savedQuotations ? JSON.parse(savedQuotations) : mockQuotations);
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    // Mock authentication - in real app, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (email && password) {
      const mockUser: QuotationUser = {
        id: 'quotation_user_1',
        email,
        name: email.split('@')[0],
        company: 'Professional Quotations LLC',
        createdAt: new Date().toISOString()
      };
      
      setUser(mockUser);
      setQuotations(mockQuotations);
      localStorage.setItem('quotationUser', JSON.stringify(mockUser));
      localStorage.setItem('quotationUserQuotations', JSON.stringify(mockQuotations));
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const register = async (email: string, password: string, name: string, company?: string): Promise<boolean> => {
    setIsLoading(true);
    
    // Mock registration - in real app, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (email && password && name) {
      const newUser: QuotationUser = {
        id: `quotation_user_${Date.now()}`,
        email,
        name,
        company,
        createdAt: new Date().toISOString()
      };
      
      setUser(newUser);
      setQuotations([]);
      localStorage.setItem('quotationUser', JSON.stringify(newUser));
      localStorage.setItem('quotationUserQuotations', JSON.stringify([]));
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const logout = () => {
    setUser(null);
    setQuotations([]);
    localStorage.removeItem('quotationUser');
    localStorage.removeItem('quotationUserQuotations');
  };

  const createQuotation = (quotationData: Omit<Quotation, 'id' | 'createdDate' | 'lastModified' | 'status'>): Quotation => {
    const newQuotation: Quotation = {
      ...quotationData,
      id: `QUO${Date.now()}`,
      status: 'draft',
      createdDate: new Date().toISOString().split('T')[0],
      lastModified: new Date().toISOString().split('T')[0]
    };
    
    const updatedQuotations = [...quotations, newQuotation];
    setQuotations(updatedQuotations);
    localStorage.setItem('quotationUserQuotations', JSON.stringify(updatedQuotations));
    
    return newQuotation;
  };

  const updateQuotation = (id: string, updates: Partial<Quotation>) => {
    const updatedQuotations = quotations.map(quotation => 
      quotation.id === id 
        ? { ...quotation, ...updates, lastModified: new Date().toISOString().split('T')[0] }
        : quotation
    );
    setQuotations(updatedQuotations);
    localStorage.setItem('quotationUserQuotations', JSON.stringify(updatedQuotations));
  };

  const deleteQuotation = (id: string) => {
    const updatedQuotations = quotations.filter(quotation => quotation.id !== id);
    setQuotations(updatedQuotations);
    localStorage.setItem('quotationUserQuotations', JSON.stringify(updatedQuotations));
  };

  const getQuotation = (id: string): Quotation | undefined => {
    return quotations.find(quotation => quotation.id === id);
  };

  const resendQuotation = async (id: string): Promise<boolean> => {
    // Mock resend functionality
    await new Promise(resolve => setTimeout(resolve, 500));
    
    updateQuotation(id, { 
      status: 'sent',
      lastModified: new Date().toISOString().split('T')[0]
    });
    
    return true;
  };

  const duplicateQuotation = (id: string): Quotation | null => {
    const originalQuotation = getQuotation(id);
    if (!originalQuotation) return null;
    
    const duplicatedQuotation = createQuotation({
      ...originalQuotation,
      title: `${originalQuotation.title} (Copy)`,
      clientName: '',
      clientEmail: ''
    });
    
    return duplicatedQuotation;
  };

  const convertToContract = (id: string): boolean => {
    const quotation = getQuotation(id);
    if (!quotation || quotation.status !== 'approved') return false;
    
    // In a real app, this would create a contract in the contract system
    // For now, we'll just update the quotation status
    updateQuotation(id, { status: 'approved' });
    return true;
  };

  const value: QuotationAuthContextType = {
    user,
    quotations,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    createQuotation,
    updateQuotation,
    deleteQuotation,
    getQuotation,
    resendQuotation,
    duplicateQuotation,
    convertToContract
  };

  return (
    <QuotationAuthContext.Provider value={value}>
      {children}
    </QuotationAuthContext.Provider>
  );
};
