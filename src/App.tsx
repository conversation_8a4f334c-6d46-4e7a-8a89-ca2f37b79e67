
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import ProjectDetails from "./pages/ProjectDetails";
import ClientPortal from "./pages/ClientPortal";
import CostTracking from "./pages/CostTracking";
import MaterialManagement from "./pages/MaterialManagement";
import ProgressTracking from "./pages/ProgressTracking";
import ContractBuilder from "./pages/ContractBuilder";
import LaborManagement from "./pages/LaborManagement";
import TaskManagement from "./pages/TaskManagement";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/project/:id" element={<ProjectDetails />} />
          <Route path="/client/:token" element={<ClientPortal />} />
          <Route path="/costs" element={<CostTracking />} />
          <Route path="/materials" element={<MaterialManagement />} />
          <Route path="/progress" element={<ProgressTracking />} />
          <Route path="/contracts" element={<ContractBuilder />} />
          <Route path="/labor" element={<LaborManagement />} />
          <Route path="/tasks" element={<TaskManagement />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
