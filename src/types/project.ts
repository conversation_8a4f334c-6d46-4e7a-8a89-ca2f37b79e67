// Project-related type definitions for ConstructionSync Hub

export interface ProjectPhase {
  id: string;
  name: string;
  description: string;
  budget: number;
  spent?: number;
  progress: number;
  status: 'pending' | 'active' | 'completed' | 'on-hold' | 'cancelled';
  startDate?: string;
  dueDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  duration: number; // in days
  dependencies: string[]; // IDs of phases this depends on
  tasks?: ProjectTask[];
  materials?: ProjectMaterial[];
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  phone?: string;
  avatar?: string;
  department?: string;
  hourlyRate?: number;
  permissions?: string[];
  isActive: boolean;
  joinedDate?: string;
}

export interface ProjectMaterial {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  supplier?: string;
  status: 'ordered' | 'delivered' | 'in-stock' | 'low-stock' | 'out-of-stock';
  deliveryDate?: string;
  category: string;
  minStock?: number;
  location?: string;
}

export interface ProjectDocument {
  id: string;
  name: string;
  type: 'permit' | 'blueprint' | 'contract' | 'report' | 'invoice' | 'photo' | 'other';
  url?: string;
  uploadDate: string;
  uploadedBy: string;
  status: 'pending' | 'approved' | 'rejected' | 'current' | 'archived';
  version?: string;
  size?: number;
  description?: string;
}

export interface ProjectTask {
  id: string;
  title: string;
  description: string;
  assigneeId: string;
  assigneeName: string;
  status: 'pending' | 'in-progress' | 'completed' | 'on-hold' | 'cancelled' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: string;
  createdDate: string;
  completedDate?: string;
  progress: number;
  estimatedHours: number;
  actualHours?: number;
  dependencies: string[];
  tags: string[];
  phaseId?: string;
}

export interface ProjectMilestone {
  id: string;
  name: string;
  description?: string;
  dueDate: string;
  completed: boolean;
  completedDate?: string;
  phaseId?: string;
}

export interface ProjectBudgetBreakdown {
  category: string;
  budgeted: number;
  spent: number;
  remaining: number;
  percentage: number;
}

export interface ProjectRisk {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  probability: 'low' | 'medium' | 'high';
  impact: string;
  mitigation: string;
  status: 'identified' | 'mitigated' | 'resolved' | 'escalated';
  identifiedDate: string;
  owner: string;
}

export interface ProjectActivity {
  id: string;
  type: 'created' | 'updated' | 'completed' | 'milestone' | 'comment' | 'file_upload' | 'status_change' | 'client_comment' | 'client_feedback';
  title: string;
  description: string;
  timestamp: string;
  userId: string;
  userName: string;
  metadata?: Record<string, any>;
  isClientVisible?: boolean; // Whether this activity should be visible to clients
}

export interface ClientComment {
  id: string;
  projectId: string;
  content: string;
  timestamp: string;
  clientName: string;
  clientEmail?: string;
  status: 'new' | 'read' | 'responded' | 'resolved';
  priority: 'low' | 'medium' | 'high';
  category: 'general' | 'concern' | 'suggestion' | 'question' | 'approval';
  attachments?: ClientCommentAttachment[];
  responses?: ClientCommentResponse[];
  isPublic: boolean; // Whether other clients can see this comment
  relatedPhaseId?: string; // If comment is related to a specific phase
  relatedTaskId?: string; // If comment is related to a specific task
}

export interface ClientCommentAttachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: string;
}

export interface ClientCommentResponse {
  id: string;
  content: string;
  timestamp: string;
  responderId: string;
  responderName: string;
  responderRole: 'contractor' | 'project_manager' | 'admin';
  isClientVisible: boolean;
}

export interface ClientPortalSettings {
  enabled: boolean;
  token: string;
  allowComments: boolean;
  allowFileUploads: boolean;
  showBudgetDetails: boolean;
  showTeamDetails: boolean;
  showDetailedProgress: boolean;
  autoNotifyOnUpdates: boolean;
  customBranding?: {
    logo?: string;
    primaryColor?: string;
    companyName?: string;
  };
  accessRestrictions?: {
    ipWhitelist?: string[];
    passwordProtected?: boolean;
    password?: string;
    expiryDate?: string;
  };
}

export interface Project {
  // Basic Information
  id: string;
  name: string;
  description: string;
  client: string;
  contractor: string;
  
  // Timeline & Status
  startDate: string;
  dueDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
  progress: number; // 0-100
  
  // Budget & Financial
  budget: number;
  spent: number;
  budgetBreakdown?: ProjectBudgetBreakdown[];
  
  // Project Details
  projectType: 'commercial' | 'residential' | 'industrial' | 'infrastructure' | 'renovation';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  location?: string;
  
  // Project Structure
  phases: ProjectPhase[];
  team: TeamMember[];
  materials?: ProjectMaterial[];
  documents?: ProjectDocument[];
  tasks?: ProjectTask[];
  milestones?: ProjectMilestone[];
  risks?: ProjectRisk[];
  
  // Metadata
  createdDate: string;
  createdBy: string;
  lastUpdate: string;
  lastUpdateBy: string;
  
  // Client Portal
  clientPortalEnabled: boolean;
  clientPortalToken?: string;
  clientPortalSettings?: ClientPortalSettings;

  // Client Communication
  clientComments?: ClientComment[];
  unreadClientComments?: number;

  // Activity Log
  activities?: ProjectActivity[];
  
  // Settings
  settings?: {
    notifications: boolean;
    autoProgressTracking: boolean;
    budgetAlerts: boolean;
    timelineAlerts: boolean;
  };
}

// Form data interface for project creation
export interface ProjectFormData {
  // Basic Information
  name: string;
  description: string;
  client: string;
  contractor: string;
  
  // Timeline & Budget
  startDate: string;
  dueDate: string;
  budget: number;
  
  // Project Details
  projectType: string;
  priority: string;
  location: string;
  
  // Phases
  phases: Omit<ProjectPhase, 'id' | 'progress' | 'status' | 'spent' | 'actualStartDate' | 'actualEndDate'>[];
  
  // Team
  team: Omit<TeamMember, 'id' | 'isActive' | 'joinedDate'>[];
}

// Validation schemas and utility functions
export const validateProjectFormData = (data: ProjectFormData): string[] => {
  const errors: string[] = [];
  
  // Basic validation
  if (!data.name.trim()) errors.push("Project name is required");
  if (!data.client.trim()) errors.push("Client name is required");
  if (!data.contractor.trim()) errors.push("Contractor name is required");
  if (!data.description.trim()) errors.push("Project description is required");
  
  // Date validation
  if (!data.startDate) errors.push("Start date is required");
  if (!data.dueDate) errors.push("Due date is required");
  if (data.startDate && data.dueDate && new Date(data.startDate) >= new Date(data.dueDate)) {
    errors.push("Due date must be after start date");
  }
  
  // Budget validation
  if (data.budget <= 0) errors.push("Budget must be greater than 0");
  
  // Phases validation
  if (data.phases.length === 0) errors.push("At least one project phase is required");
  data.phases.forEach((phase, index) => {
    if (!phase.name.trim()) errors.push(`Phase ${index + 1} name is required`);
    if (phase.budget <= 0) errors.push(`Phase ${index + 1} budget must be greater than 0`);
    if (phase.duration <= 0) errors.push(`Phase ${index + 1} duration must be greater than 0`);
  });
  
  // Team validation
  if (data.team.length === 0) errors.push("At least one team member is required");
  data.team.forEach((member, index) => {
    if (!member.name.trim()) errors.push(`Team member ${index + 1} name is required`);
    if (!member.email.trim()) errors.push(`Team member ${index + 1} email is required`);
    if (!member.role.trim()) errors.push(`Team member ${index + 1} role is required`);
    
    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (member.email && !emailRegex.test(member.email)) {
      errors.push(`Team member ${index + 1} email format is invalid`);
    }
  });
  
  return errors;
};

// Utility function to convert form data to project
export const convertFormDataToProject = (
  formData: ProjectFormData, 
  userId: string, 
  userName: string
): Project => {
  const now = new Date().toISOString();
  const projectId = `project_${Date.now()}`;
  
  return {
    id: projectId,
    name: formData.name,
    description: formData.description,
    client: formData.client,
    contractor: formData.contractor,
    startDate: formData.startDate,
    dueDate: formData.dueDate,
    status: 'planning',
    progress: 0,
    budget: formData.budget,
    spent: 0,
    projectType: formData.projectType as Project['projectType'],
    priority: formData.priority as Project['priority'],
    location: formData.location,
    phases: formData.phases.map((phase, index) => ({
      ...phase,
      id: `phase_${projectId}_${index}`,
      progress: 0,
      status: 'pending' as const,
      spent: 0
    })),
    team: formData.team.map((member, index) => ({
      ...member,
      id: `member_${projectId}_${index}`,
      isActive: true,
      joinedDate: now
    })),
    createdDate: now,
    createdBy: userId,
    lastUpdate: now,
    lastUpdateBy: userName,
    clientPortalEnabled: true,
    clientPortalToken: `${projectId}-${Math.random().toString(36).substr(2, 9)}`,
    activities: [{
      id: `activity_${Date.now()}`,
      type: 'created',
      title: 'Project Created',
      description: `Project "${formData.name}" was created`,
      timestamp: now,
      userId,
      userName
    }],
    settings: {
      notifications: true,
      autoProgressTracking: true,
      budgetAlerts: true,
      timelineAlerts: true
    }
  };
};

// Utility functions for client comments
export const createClientComment = (
  projectId: string,
  content: string,
  clientName: string,
  clientEmail?: string,
  category: ClientComment['category'] = 'general',
  priority: ClientComment['priority'] = 'medium'
): ClientComment => {
  return {
    id: `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    projectId,
    content,
    timestamp: new Date().toISOString(),
    clientName,
    clientEmail,
    status: 'new',
    priority,
    category,
    attachments: [],
    responses: [],
    isPublic: true
  };
};

export const addCommentResponse = (
  comment: ClientComment,
  content: string,
  responderId: string,
  responderName: string,
  responderRole: ClientCommentResponse['responderRole'] = 'contractor'
): ClientComment => {
  const response: ClientCommentResponse = {
    id: `response_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    content,
    timestamp: new Date().toISOString(),
    responderId,
    responderName,
    responderRole,
    isClientVisible: true
  };

  return {
    ...comment,
    responses: [...(comment.responses || []), response],
    status: 'responded'
  };
};

export const getCommentsByStatus = (comments: ClientComment[], status: ClientComment['status']): ClientComment[] => {
  return comments.filter(comment => comment.status === status);
};

export const getCommentsByPriority = (comments: ClientComment[], priority: ClientComment['priority']): ClientComment[] => {
  return comments.filter(comment => comment.priority === priority);
};

export const getUnreadCommentsCount = (comments: ClientComment[]): number => {
  return comments.filter(comment => comment.status === 'new').length;
};

export const markCommentAsRead = (comment: ClientComment): ClientComment => {
  return {
    ...comment,
    status: comment.status === 'new' ? 'read' : comment.status
  };
};

export const resolveComment = (comment: ClientComment): ClientComment => {
  return {
    ...comment,
    status: 'resolved'
  };
};

// Default client portal settings
export const getDefaultClientPortalSettings = (projectId: string): ClientPortalSettings => {
  return {
    enabled: true,
    token: `${projectId}-client-${Math.random().toString(36).substr(2, 9)}`,
    allowComments: true,
    allowFileUploads: false,
    showBudgetDetails: true,
    showTeamDetails: true,
    showDetailedProgress: true,
    autoNotifyOnUpdates: true
  };
};

export default Project;
