export interface WebTemplate {
  id: string;
  name: string;
  description: string;
  category: 'modern' | 'classic' | 'minimal' | 'creative' | 'professional';
  type: 'contract' | 'quotation';
  features: string[];
  preview: string;
  rating: number;
  downloads: number;
  isInteractive: boolean;
  hasSignature: boolean;
  mobileOptimized: boolean;
}

export const contractWebTemplates: WebTemplate[] = [
  {
    id: 'modern-interactive-contract',
    name: 'Modern Interactive Contract',
    description: 'A sleek, modern contract with interactive elements, progress tracking, and digital signatures',
    category: 'modern',
    type: 'contract',
    features: [
      'Interactive form fields',
      'Progress tracking',
      'Digital signature pad',
      'Real-time validation',
      'Mobile responsive',
      'Auto-save functionality'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.9,
    downloads: 1247,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'classic-formal-contract',
    name: 'Classic Formal Contract',
    description: 'Traditional formal contract layout with elegant typography and professional styling',
    category: 'classic',
    type: 'contract',
    features: [
      'Formal document structure',
      'Elegant typography',
      'Professional layout',
      'Digital signatures',
      'Print-friendly design',
      'Legal compliance ready'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.8,
    downloads: 892,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'minimal-clean-contract',
    name: 'Minimal Clean Contract',
    description: 'Clean, minimalist design focusing on clarity and ease of use',
    category: 'minimal',
    type: 'contract',
    features: [
      'Clean minimal design',
      'Easy to read layout',
      'Simple navigation',
      'Touch-friendly interface',
      'Fast loading',
      'Accessibility optimized'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.7,
    downloads: 654,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'creative-visual-contract',
    name: 'Creative Visual Contract',
    description: 'Visually engaging contract with icons, progress bars, and modern UI elements',
    category: 'creative',
    type: 'contract',
    features: [
      'Visual progress indicators',
      'Icon-based sections',
      'Animated interactions',
      'Color-coded sections',
      'Modern UI elements',
      'Engaging user experience'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.6,
    downloads: 543,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'professional-corporate-contract',
    name: 'Professional Corporate Contract',
    description: 'Corporate-grade contract template with advanced features and compliance tools',
    category: 'professional',
    type: 'contract',
    features: [
      'Corporate branding',
      'Advanced form validation',
      'Compliance tracking',
      'Multi-step workflow',
      'Document versioning',
      'Audit trail'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.9,
    downloads: 1156,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  }
];

export const quotationWebTemplates: WebTemplate[] = [
  {
    id: 'modern-interactive-quotation',
    name: 'Modern Interactive Quotation',
    description: 'Dynamic quotation form with real-time calculations and interactive pricing',
    category: 'modern',
    type: 'quotation',
    features: [
      'Real-time calculations',
      'Interactive pricing',
      'Dynamic line items',
      'Instant totals',
      'Mobile responsive',
      'Client approval workflow'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.8,
    downloads: 987,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'classic-detailed-quotation',
    name: 'Classic Detailed Quotation',
    description: 'Comprehensive quotation template with detailed breakdowns and formal presentation',
    category: 'classic',
    type: 'quotation',
    features: [
      'Detailed cost breakdown',
      'Professional formatting',
      'Multiple pricing tiers',
      'Terms and conditions',
      'Digital acceptance',
      'PDF generation'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.7,
    downloads: 743,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'minimal-quick-quotation',
    name: 'Minimal Quick Quotation',
    description: 'Streamlined quotation form for fast quotes and quick client responses',
    category: 'minimal',
    type: 'quotation',
    features: [
      'Quick quote generation',
      'Simplified interface',
      'One-page layout',
      'Fast client response',
      'Mobile-first design',
      'Instant sharing'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.6,
    downloads: 567,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'creative-visual-quotation',
    name: 'Creative Visual Quotation',
    description: 'Visually stunning quotation with charts, graphs, and interactive elements',
    category: 'creative',
    type: 'quotation',
    features: [
      'Visual cost charts',
      'Interactive graphs',
      'Progress visualization',
      'Animated elements',
      'Modern aesthetics',
      'Engaging presentation'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.5,
    downloads: 432,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'professional-enterprise-quotation',
    name: 'Professional Enterprise Quotation',
    description: 'Enterprise-level quotation with advanced features and business intelligence',
    category: 'professional',
    type: 'quotation',
    features: [
      'Advanced analytics',
      'Multi-currency support',
      'Approval workflows',
      'Integration ready',
      'Custom branding',
      'Enterprise security'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.9,
    downloads: 1089,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  },
  {
    id: 'construction-specific-quotation',
    name: 'Construction-Specific Quotation',
    description: 'Specialized quotation template designed specifically for construction projects',
    category: 'professional',
    type: 'quotation',
    features: [
      'Construction categories',
      'Material calculators',
      'Labor cost tracking',
      'Timeline integration',
      'Permit tracking',
      'Subcontractor management'
    ],
    preview: '/api/placeholder/400/300',
    rating: 4.8,
    downloads: 876,
    isInteractive: true,
    hasSignature: true,
    mobileOptimized: true
  }
];

export const getAllWebTemplates = () => {
  return [...contractWebTemplates, ...quotationWebTemplates];
};

export const getWebTemplatesByType = (type: 'contract' | 'quotation') => {
  return type === 'contract' ? contractWebTemplates : quotationWebTemplates;
};

export const getWebTemplateById = (id: string) => {
  return getAllWebTemplates().find(template => template.id === id);
};
