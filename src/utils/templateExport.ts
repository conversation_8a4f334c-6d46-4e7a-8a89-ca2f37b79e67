interface TemplateCustomization {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  companyName: string;
  companyAddress: string;
  companyPhone: string;
  companyEmail: string;
  companyWebsite: string;
  logoUrl: string;
  logoFile: File | null;
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  type: string;
}

export const generateWebBasedTemplate = (
  template: Template,
  customization: TemplateCustomization,
  isContract: boolean = true
): string => {
  const documentType = isContract ? "CONTRACT" : "QUOTATION";
  const documentTitle = isContract ? "Construction Contract" : "Project Quotation";
  const uniqueId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${customization.companyName || "Company"} - ${documentTitle}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, ${customization.backgroundColor} 0%, #f8fafc 100%);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: ${customization.primaryColor};
            color: ${customization.backgroundColor};
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .company-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: ${customization.backgroundColor};
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: ${customization.primaryColor};
        }
        .company-details h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .company-details p {
            margin: 5px 0;
            opacity: 0.9;
        }
        .contact-info {
            text-align: right;
            font-size: 14px;
        }
        .section {
            margin: 30px 0;
        }
        .section h2 {
            color: ${customization.accentColor};
            border-bottom: 2px solid ${customization.secondaryColor};
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .info-block h3 {
            color: ${customization.accentColor};
            margin-bottom: 10px;
        }
        .info-block p {
            margin: 5px 0;
            font-size: 14px;
        }
        .project-details {
            background-color: ${customization.backgroundColor};
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid ${customization.primaryColor};
        }
        .cost-breakdown {
            margin-top: 20px;
        }
        .cost-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid ${customization.secondaryColor};
        }
        .cost-total {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            font-weight: bold;
            font-size: 18px;
            color: ${customization.accentColor};
            border-top: 2px solid ${customization.primaryColor};
            margin-top: 10px;
        }
        .terms {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .signature-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        .signature-block {
            text-align: center;
            padding: 20px;
            border: 1px solid ${customization.secondaryColor};
            border-radius: 8px;
        }
        .signature-line {
            border-bottom: 1px solid ${customization.textColor};
            margin: 20px 0 10px 0;
            height: 40px;
        }
        @media print {
            body { padding: 0; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                ${customization.logoUrl ? 
                    `<img src="${customization.logoUrl}" alt="Company Logo" style="width: 80px; height: 80px; object-fit: contain; background: white; border-radius: 8px; padding: 8px;">` :
                    `<div class="logo">${(customization.companyName || "LOGO").substring(0, 2).toUpperCase()}</div>`
                }
                <div class="company-details">
                    <h1>${customization.companyName || "Your Company Name"}</h1>
                    <p>${documentTitle}</p>
                    <p>Template: ${template.name}</p>
                </div>
            </div>
            <div class="contact-info">
                <p>${customization.companyPhone || "(*************"}</p>
                <p>${customization.companyEmail || "<EMAIL>"}</p>
                ${customization.companyWebsite ? `<p>${customization.companyWebsite}</p>` : ""}
            </div>
        </div>

        <!-- Company and Client Information -->
        <div class="section">
            <h2>Project Information</h2>
            <div class="info-grid">
                <div class="info-block">
                    <h3>Company Details</h3>
                    <p><strong>${customization.companyName || "Your Company Name"}</strong></p>
                    <p>${customization.companyAddress || "123 Business Street, City, State 12345"}</p>
                    <p>Phone: ${customization.companyPhone || "(*************"}</p>
                    <p>Email: ${customization.companyEmail || "<EMAIL>"}</p>
                    ${customization.companyWebsite ? `<p>Website: ${customization.companyWebsite}</p>` : ""}
                </div>
                <div class="info-block">
                    <h3>Client Information</h3>
                    <p><strong>John & Jane Smith</strong></p>
                    <p>456 Client Avenue, City, State 12345</p>
                    <p>Phone: (*************</p>
                    <p>Email: <EMAIL></p>
                </div>
            </div>
        </div>

        <!-- Project Details -->
        <div class="section">
            <h2>Project Scope</h2>
            <div class="project-details">
                <h3>Project Description</h3>
                <p>${isContract ? 
                    "Construction of a residential home including foundation, framing, electrical, plumbing, and finishing work. This contract outlines the terms and conditions for the complete construction project." :
                    "Comprehensive quotation for residential construction project including detailed breakdown of materials, labor costs, timeline estimates, and project specifications."
                }</p>
                
                <h3>Project Timeline</h3>
                <p><strong>Start Date:</strong> [To be filled]</p>
                <p><strong>Estimated Completion:</strong> [To be filled]</p>
                <p><strong>Project Duration:</strong> [To be filled]</p>
            </div>
        </div>

        <!-- Cost Breakdown -->
        <div class="section">
            <h2>${isContract ? "Contract Value" : "Cost Estimate"}</h2>
            <div class="cost-breakdown">
                <div class="cost-item">
                    <span>Materials & Supplies</span>
                    <span>$125,000.00</span>
                </div>
                <div class="cost-item">
                    <span>Labor Costs</span>
                    <span>$85,000.00</span>
                </div>
                <div class="cost-item">
                    <span>Equipment & Tools</span>
                    <span>$15,000.00</span>
                </div>
                <div class="cost-item">
                    <span>Permits & Fees</span>
                    <span>$5,000.00</span>
                </div>
                <div class="cost-total">
                    <span>Total Project Cost</span>
                    <span>$230,000.00</span>
                </div>
            </div>
        </div>

        ${isContract ? `
        <!-- Terms and Conditions -->
        <div class="section">
            <h2>Terms and Conditions</h2>
            <div class="terms">
                <p><strong>Payment Terms:</strong> 20% down payment, 30% at foundation completion, 30% at framing completion, 20% upon final completion.</p>
                <p><strong>Change Orders:</strong> Any changes to the original scope must be approved in writing and may affect the total cost and timeline.</p>
                <p><strong>Warranty:</strong> All work is guaranteed for one year from completion date.</p>
                <p><strong>Insurance:</strong> Contractor maintains full liability and workers' compensation insurance.</p>
            </div>
        </div>

        <!-- Signatures -->
        <div class="signature-section">
            <div class="signature-block">
                <h3>Contractor</h3>
                <div class="signature-line"></div>
                <p><strong>${customization.companyName || "Your Company Name"}</strong></p>
                <p>Date: _______________</p>
            </div>
            <div class="signature-block">
                <h3>Client</h3>
                <div class="signature-line"></div>
                <p><strong>Client Name</strong></p>
                <p>Date: _______________</p>
            </div>
        </div>
        ` : `
        <!-- Next Steps -->
        <div class="section">
            <h2>Next Steps</h2>
            <div class="terms">
                <p><strong>Quotation Valid Until:</strong> [Date - typically 30 days]</p>
                <p><strong>To Accept:</strong> Please sign and return this quotation along with the required deposit.</p>
                <p><strong>Questions:</strong> Contact us at ${customization.companyPhone || "(*************"} or ${customization.companyEmail || "<EMAIL>"}</p>
                <p><strong>Project Start:</strong> Work will commence within [timeframe] of signed agreement and deposit receipt.</p>
            </div>
        </div>

        <!-- Acceptance -->
        <div class="signature-section">
            <div class="signature-block">
                <h3>Prepared By</h3>
                <div class="signature-line"></div>
                <p><strong>${customization.companyName || "Your Company Name"}</strong></p>
                <p>Date: _______________</p>
            </div>
            <div class="signature-block">
                <h3>Client Acceptance</h3>
                <div class="signature-line"></div>
                <p><strong>Client Signature</strong></p>
                <p>Date: _______________</p>
            </div>
        </div>
        `}
    </div>
</body>
</html>
  `.trim();
};

export const downloadTemplate = (
  template: Template,
  customization: TemplateCustomization,
  isContract: boolean = true
) => {
  const htmlContent = generateCustomizedTemplate(template, customization, isContract);
  const blob = new Blob([htmlContent], { type: 'text/html' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${customization.companyName || 'Company'}_${isContract ? 'Contract' : 'Quotation'}_${template.name.replace(/\s+/g, '_')}.html`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const generatePDF = async (
  template: Template,
  customization: TemplateCustomization,
  isContract: boolean = true
) => {
  // This would typically use a library like jsPDF or Puppeteer
  // For now, we'll create a print-friendly version
  const htmlContent = generateCustomizedTemplate(template, customization, isContract);
  
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    
    // Wait for content to load then trigger print
    setTimeout(() => {
      printWindow.print();
    }, 500);
  }
};
