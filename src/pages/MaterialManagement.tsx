
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Plus, FileText, Search, Users, Calendar } from "lucide-react";
import DashboardLayout from "@/components/DashboardLayout";

const MaterialManagement = () => {
  const materials = [
    {
      id: "1",
      name: "Steel Reinforcement Bars",
      project: "Downtown Office Complex",
      quantity: 150,
      unit: "pieces",
      unitCost: 125,
      totalCost: 18750,
      supplier: "SteelCorp Inc.",
      status: "in-stock",
      minStock: 50,
      deliveryDate: "Nov 15, 2025",
      category: "structural"
    },
    {
      id: "2",
      name: "Concrete Mix",
      project: "Downtown Office Complex", 
      quantity: 25,
      unit: "cubic yards",
      unitCost: 150,
      totalCost: 3750,
      supplier: "ConcretePlus",
      status: "low-stock",
      minStock: 10,
      deliveryDate: "Nov 18, 2025",
      category: "foundation"
    },
    {
      id: "3",
      name: "Electrical Wire (12 AWG)",
      project: "Residential Villa",
      quantity: 500,
      unit: "feet",
      unitCost: 2.5,
      totalCost: 1250,
      supplier: "ElectroSupply",
      status: "ordered",
      minStock: 200,
      deliveryDate: "Nov 20, 2025",
      category: "electrical"
    },
    {
      id: "4",
      name: "Insulation Batts", 
      project: "Residential Villa",
      quantity: 80,
      unit: "packages",
      unitCost: 45,
      totalCost: 3600,
      supplier: "InsulMax",
      status: "delivered",
      minStock: 20,
      deliveryDate: "Nov 10, 2025",
      category: "insulation"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "in-stock": return "bg-green-500";
      case "low-stock": return "bg-yellow-500";
      case "ordered": return "bg-blue-500";
      case "delivered": return "bg-purple-500";
      case "out-of-stock": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "structural": return "bg-orange-100 text-orange-700";
      case "foundation": return "bg-gray-100 text-gray-700";
      case "electrical": return "bg-yellow-100 text-yellow-700";
      case "insulation": return "bg-blue-100 text-blue-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const totalValue = materials.reduce((sum, m) => sum + m.totalCost, 0);
  const lowStockItems = materials.filter(m => m.status === "low-stock" || m.quantity <= m.minStock).length;
  const orderedItems = materials.filter(m => m.status === "ordered").length;

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Material Management & Inventory</h1>
            <p className="text-muted-foreground">
              Track materials, monitor inventory levels, and manage supplier relationships
            </p>
          </div>
          <div className="flex space-x-2 mt-4 md:mt-0">
            <Button variant="outline">
              <Search className="mr-2 h-4 w-4" />
              Find Suppliers
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Material
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Inventory Value</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalValue / 1000).toFixed(1)}K</div>
              <p className="text-xs text-muted-foreground">
                Across all materials
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock Alerts</CardTitle>
              <div className={`w-3 h-3 rounded-full ${lowStockItems > 0 ? 'bg-yellow-500' : 'bg-green-500'}`}></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{lowStockItems}</div>
              <p className="text-xs text-muted-foreground">
                Items need reordering
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{orderedItems}</div>
              <p className="text-xs text-muted-foreground">
                Materials in transit
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Suppliers</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">4</div>
              <p className="text-xs text-muted-foreground">
                Supplier relationships
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Materials Inventory */}
        <Card>
          <CardHeader>
            <CardTitle>Material Inventory</CardTitle>
            <CardDescription>
              Complete overview of all materials across projects with real-time stock levels
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {materials.map((material) => {
                const stockPercentage = Math.min((material.quantity / (material.minStock * 3)) * 100, 100);
                
                return (
                  <div key={material.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4">
                      <div className="flex items-center space-x-3 mb-2 lg:mb-0">
                        <div>
                          <h3 className="font-semibold text-lg">{material.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {material.project} • {material.supplier}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getCategoryColor(material.category)}>
                          {material.category}
                        </Badge>
                        <Badge variant="outline" className={`${getStatusColor(material.status)} text-white`}>
                          {material.status.replace('-', ' ')}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Quantity & Stock</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm">Current Stock</span>
                            <span className="font-medium">{material.quantity} {material.unit}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm">Min Stock Level</span>
                            <span className="text-sm text-muted-foreground">{material.minStock} {material.unit}</span>
                          </div>
                          <Progress value={stockPercentage} className="h-2" />
                          {material.quantity <= material.minStock && (
                            <p className="text-xs text-yellow-600">⚠ Below minimum stock level</p>
                          )}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-sm mb-2">Cost Information</h4>
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <span className="text-sm">Unit Cost</span>
                            <span className="font-medium">${material.unitCost}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm">Total Value</span>
                            <span className="font-medium">${material.totalCost.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-sm mb-2">Supplier & Delivery</h4>
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{material.supplier}</p>
                          <p className="text-xs text-muted-foreground">
                            Delivery: {material.deliveryDate}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex flex-col space-y-2">
                        <Button variant="outline" size="sm">
                          Generate Purchase Order
                        </Button>
                        <Button variant="ghost" size="sm">
                          View Details
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Supplier Integration */}
        <div className="grid lg:grid-cols-2 gap-8 mt-8">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardHeader>
              <CardTitle>Supplier Marketplace</CardTitle>
              <CardDescription>
                Compare prices and manage supplier relationships
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 border rounded">
                  <div>
                    <p className="font-medium">SteelCorp Inc.</p>
                    <p className="text-xs text-muted-foreground">Steel & Rebar Specialist</p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                <div className="flex justify-between items-center p-3 border rounded">
                  <div>
                    <p className="font-medium">ConcretePlus</p>
                    <p className="text-xs text-muted-foreground">Concrete & Aggregates</p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                <Button variant="outline" className="w-full">
                  Find New Suppliers
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
            <CardHeader>
              <CardTitle>Automated Reordering</CardTitle>
              <CardDescription>
                Smart inventory management with automatic reorder points
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto-reorder enabled</span>
                  <Badge variant="default" className="bg-green-500">ON</Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Items monitored</span>
                    <span className="font-medium">{materials.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Reorder threshold</span>
                    <span className="font-medium">Min stock level</span>
                  </div>
                </div>
                <Button variant="outline" className="w-full">
                  Configure Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default MaterialManagement;
