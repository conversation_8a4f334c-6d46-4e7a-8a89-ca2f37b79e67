import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Calculator, 
  Plus, 
  Send, 
  Download, 
  Users, 
  Trash2,
  Settings,
  FileText,
  DollarSign
} from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

const QuotationBuilder = () => {
  const [quotationData, setQuotationData] = useState({
    title: "",
    clientName: "",
    clientEmail: "",
    projectDescription: "",
    validUntil: "",
    notes: ""
  });

  const [lineItems, setLineItems] = useState([
    { id: 1, description: "", quantity: "", unit: "", unitPrice: "", total: 0 }
  ]);

  const addLineItem = () => {
    const newId = Math.max(...lineItems.map(item => item.id)) + 1;
    setLineItems([...lineItems, { 
      id: newId, 
      description: "", 
      quantity: "", 
      unit: "", 
      unitPrice: "", 
      total: 0 
    }]);
  };

  const removeLineItem = (id: number) => {
    if (lineItems.length > 1) {
      setLineItems(lineItems.filter(item => item.id !== id));
    }
  };

  const updateLineItem = (id: number, field: string, value: string) => {
    setLineItems(lineItems.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        if (field === 'quantity' || field === 'unitPrice') {
          const quantity = parseFloat(updatedItem.quantity) || 0;
          const unitPrice = parseFloat(updatedItem.unitPrice) || 0;
          updatedItem.total = quantity * unitPrice;
        }
        return updatedItem;
      }
      return item;
    }));
  };

  const calculateSubtotal = () => {
    return lineItems.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTax = (subtotal: number, taxRate: number = 0.1) => {
    return subtotal * taxRate;
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax(subtotal);
    return subtotal + tax;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link to="/professional-selection" className="flex items-center space-x-2">
              <div className="h-8 w-8 construction-gradient rounded-lg flex items-center justify-center">
                <Settings className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                ConstructionSync
              </h1>
            </Link>
            <div className="flex items-center space-x-4">
              <Link to="/contracts" className="text-muted-foreground hover:text-primary transition-colors">
                Contract Builder
              </Link>
              <Link to="/login" className="text-muted-foreground hover:text-primary transition-colors">
                Sign In
              </Link>
              <Button variant="outline" asChild>
                <Link to="/register">Create Account</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              Professional Quotation Builder
            </h1>
            <p className="text-lg text-muted-foreground">
              Create detailed, professional quotations with automatic calculations and client sharing
            </p>
          </div>

          {/* Quotation Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="mr-2 h-5 w-5" />
                Create New Quotation
              </CardTitle>
              <CardDescription>
                Fill in the details below to generate a professional quotation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Quotation Title</Label>
                  <Input
                    id="title"
                    placeholder="e.g., Kitchen Renovation Quote"
                    value={quotationData.title}
                    onChange={(e) => setQuotationData({...quotationData, title: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="validUntil">Valid Until</Label>
                  <Input
                    id="validUntil"
                    type="date"
                    value={quotationData.validUntil}
                    onChange={(e) => setQuotationData({...quotationData, validUntil: e.target.value})}
                  />
                </div>
              </div>

              {/* Client Information */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Client Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="clientName">Client Name</Label>
                    <Input
                      id="clientName"
                      placeholder="John Smith / ABC Company"
                      value={quotationData.clientName}
                      onChange={(e) => setQuotationData({...quotationData, clientName: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="clientEmail">Client Email</Label>
                    <Input
                      id="clientEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={quotationData.clientEmail}
                      onChange={(e) => setQuotationData({...quotationData, clientEmail: e.target.value})}
                    />
                  </div>
                </div>
              </div>

              {/* Project Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Project Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the work to be performed..."
                  value={quotationData.projectDescription}
                  onChange={(e) => setQuotationData({...quotationData, projectDescription: e.target.value})}
                  rows={3}
                />
              </div>

              {/* Line Items */}
              <div className="border-t pt-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Line Items</h3>
                  <Button onClick={addLineItem} size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Item
                  </Button>
                </div>
                
                <div className="space-y-4">
                  {lineItems.map((item, index) => (
                    <div key={item.id} className="grid grid-cols-12 gap-2 items-end p-4 border rounded-lg">
                      <div className="col-span-12 md:col-span-4">
                        <Label className="text-xs">Description</Label>
                        <Input
                          placeholder="Item description"
                          value={item.description}
                          onChange={(e) => updateLineItem(item.id, 'description', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                      <div className="col-span-3 md:col-span-2">
                        <Label className="text-xs">Quantity</Label>
                        <Input
                          type="number"
                          placeholder="1"
                          value={item.quantity}
                          onChange={(e) => updateLineItem(item.id, 'quantity', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                      <div className="col-span-3 md:col-span-2">
                        <Label className="text-xs">Unit</Label>
                        <Select 
                          value={item.unit}
                          onValueChange={(value) => updateLineItem(item.id, 'unit', value)}
                        >
                          <SelectTrigger className="text-sm">
                            <SelectValue placeholder="Unit" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="each">Each</SelectItem>
                            <SelectItem value="sqft">Sq Ft</SelectItem>
                            <SelectItem value="hour">Hour</SelectItem>
                            <SelectItem value="day">Day</SelectItem>
                            <SelectItem value="linear-ft">Linear Ft</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="col-span-3 md:col-span-2">
                        <Label className="text-xs">Unit Price</Label>
                        <Input
                          type="number"
                          placeholder="0.00"
                          value={item.unitPrice}
                          onChange={(e) => updateLineItem(item.id, 'unitPrice', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                      <div className="col-span-2 md:col-span-1">
                        <Label className="text-xs">Total</Label>
                        <div className="text-sm font-medium p-2 bg-muted rounded">
                          ${item.total.toFixed(2)}
                        </div>
                      </div>
                      <div className="col-span-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeLineItem(item.id)}
                          disabled={lineItems.length === 1}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Totals */}
              <div className="border-t pt-6">
                <div className="max-w-md ml-auto space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span className="font-medium">${calculateSubtotal().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax (10%):</span>
                    <span className="font-medium">${calculateTax(calculateSubtotal()).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t pt-2">
                    <span>Total:</span>
                    <span>${calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Terms and conditions, payment terms, etc..."
                  value={quotationData.notes}
                  onChange={(e) => setQuotationData({...quotationData, notes: e.target.value})}
                  rows={3}
                />
              </div>

              {/* Actions */}
              <div className="border-t pt-6">
                <div className="flex flex-col sm:flex-row justify-between gap-4">
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button variant="outline">
                      <Download className="mr-2 h-4 w-4" />
                      Download PDF
                    </Button>
                    <Button variant="outline">
                      Save Draft
                    </Button>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button variant="outline">
                      <Users className="mr-2 h-4 w-4" />
                      Generate Link
                    </Button>
                    <Button>
                      <Send className="mr-2 h-4 w-4" />
                      Send to Client
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default QuotationBuilder;
