import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  FileText, 
  CheckCircle, 
  Calendar, 
  DollarSign, 
  MapPin, 
  User, 
  Mail, 
  Phone,
  Building,
  Clock,
  Shield,
  PenTool
} from "lucide-react";

const PublicContractView = () => {
  const { contractId } = useParams();
  const [signature, setSignature] = useState('');
  const [clientName, setClientName] = useState('');
  const [signedDate, setSignedDate] = useState('');
  const [isSigned, setIsSigned] = useState(false);

  // Mock contract data - in real app, this would be fetched based on contractId
  const contractData = {
    id: contractId,
    title: "Kitchen Renovation Contract",
    clientName: "<PERSON>",
    clientEmail: "<EMAIL>",
    clientPhone: "(*************",
    projectType: "renovation",
    projectAddress: "123 Main St, Anytown, ST 12345",
    startDate: "2024-02-01",
    endDate: "2024-03-15",
    totalAmount: "25000",
    description: "Complete kitchen renovation including new cabinets, countertops, appliances, and flooring.",
    scope: "Full kitchen remodel with modern design elements, energy-efficient appliances, and premium finishes.",
    terms: "50% payment due upon signing, 25% at project midpoint, 25% upon completion. All work guaranteed for 2 years.",
    paymentSchedule: "50% upfront ($12,500), 25% at midpoint ($6,250), 25% on completion ($6,250)",
    materials: "Premium hardwood cabinets, quartz countertops, stainless steel appliances, luxury vinyl plank flooring",
    laborDetails: "Professional team of 4-6 workers, project manager on-site daily, all workers licensed and insured",
    warranties: "2-year warranty on all workmanship, manufacturer warranties on all appliances and materials",
    status: 'sent',
    createdAt: "2024-01-15"
  };

  const handleSign = () => {
    if (!signature.trim() || !clientName.trim()) {
      alert('Please enter your full name and signature to proceed.');
      return;
    }
    
    setSignedDate(new Date().toISOString().split('T')[0]);
    setIsSigned(true);
    
    // In a real app, this would send the signature data to the server
    alert('✅ Contract signed successfully!\n\nThe contractor has been notified of your signature. You will receive a copy via email shortly.');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
              <FileText className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{contractData.title}</h1>
          <p className="text-gray-600">Professional Construction Contract</p>
          <Badge variant="outline" className="mt-2">
            Contract ID: {contractData.id}
          </Badge>
        </div>

        {/* Status Banner */}
        {isSigned ? (
          <Card className="mb-6 bg-green-50 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="font-semibold text-green-900">Contract Signed Successfully</h3>
                  <p className="text-green-700 text-sm">Signed on {new Date(signedDate).toLocaleDateString()} by {clientName}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="mb-6 bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-blue-900">Awaiting Your Signature</h3>
                  <p className="text-blue-700 text-sm">Please review the contract details below and sign when ready</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Contract Details */}
        <div className="space-y-6">
          {/* Client Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Client Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Client Name</p>
                    <p className="font-semibold">{contractData.clientName}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-semibold">{contractData.clientEmail}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-semibold">{contractData.clientPhone}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Project Address</p>
                    <p className="font-semibold">{contractData.projectAddress}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Project Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Start Date</p>
                    <p className="font-semibold">{new Date(contractData.startDate).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Completion Date</p>
                    <p className="font-semibold">{new Date(contractData.endDate).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Total Amount</p>
                    <p className="font-semibold text-green-600">${parseInt(contractData.totalAmount).toLocaleString()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Project Type</p>
                    <p className="font-semibold capitalize">{contractData.projectType}</p>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h4 className="font-semibold mb-2">Project Description</h4>
                <p className="text-gray-700">{contractData.description}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Scope of Work</h4>
                <p className="text-gray-700">{contractData.scope}</p>
              </div>
            </CardContent>
          </Card>

          {/* Financial Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Financial Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Payment Schedule</h4>
                <p className="text-gray-700">{contractData.paymentSchedule}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Materials & Specifications</h4>
                <p className="text-gray-700">{contractData.materials}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Labor Details</h4>
                <p className="text-gray-700">{contractData.laborDetails}</p>
              </div>
            </CardContent>
          </Card>

          {/* Terms & Warranties */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Terms & Warranties
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Terms & Conditions</h4>
                <p className="text-gray-700">{contractData.terms}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Warranties & Guarantees</h4>
                <p className="text-gray-700">{contractData.warranties}</p>
              </div>
            </CardContent>
          </Card>

          {/* Digital Signature */}
          {!isSigned && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PenTool className="h-5 w-5" />
                  Digital Signature
                </CardTitle>
                <CardDescription>
                  By signing below, you agree to all terms and conditions outlined in this contract
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="clientName">Full Name *</Label>
                    <Input
                      id="clientName"
                      placeholder="Enter your full legal name"
                      value={clientName}
                      onChange={(e) => setClientName(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signature">Digital Signature *</Label>
                    <Input
                      id="signature"
                      placeholder="Type your full name as signature"
                      value={signature}
                      onChange={(e) => setSignature(e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="pt-4">
                  <Button 
                    onClick={handleSign}
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                    size="lg"
                  >
                    <PenTool className="h-5 w-5 mr-2" />
                    Sign Contract
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Footer */}
        <div className="text-center mt-8 pt-8 border-t">
          <p className="text-gray-500 text-sm">
            This is a legally binding contract. Please read all terms carefully before signing.
          </p>
          <p className="text-gray-400 text-xs mt-2">
            Generated by ConstructionSync Contract Builder
          </p>
        </div>
      </div>
    </div>
  );
};

export default PublicContractView;
