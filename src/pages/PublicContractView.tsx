import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  FileText,
  CheckCircle,
  Calendar,
  DollarSign,
  MapPin,
  User,
  Mail,
  Phone,
  Building,
  Clock,
  Shield,
  PenTool,
  Download,
  Share2,
  Star,
  Award
} from "lucide-react";

const PublicContractView = () => {
  const { contractId } = useParams();
  const [signature, setSignature] = useState('');
  const [clientName, setClientName] = useState('');
  const [signedDate, setSignedDate] = useState('');
  const [isSigned, setIsSigned] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [contractData, setContractData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Load contract data from localStorage
  useEffect(() => {
    if (contractId) {
      const storedContract = localStorage.getItem(`contract_${contractId}`);
      if (storedContract) {
        const parsedContract = JSON.parse(storedContract);
        setContractData(parsedContract);
      } else {
        // Fallback to mock data if not found
        setContractData({
          id: contractId,
          title: "Sample Contract",
          clientName: "Client Name",
          clientEmail: "<EMAIL>",
          clientPhone: "(*************",
          projectType: "renovation",
          projectAddress: "Project Address",
          startDate: "2024-02-01",
          endDate: "2024-03-15",
          totalAmount: "25000",
          description: "Project description will appear here.",
          scope: "Project scope details.",
          terms: "Terms and conditions.",
          paymentSchedule: "Payment schedule details",
          materials: "Materials list",
          laborDetails: "Labor details",
          warranties: "Warranty information",
          status: 'sent',
          createdAt: new Date().toISOString(),
          primaryColor: "#3b82f6",
          companyName: "Construction Company",
          companyLogo: null
        });
      }
      setLoading(false);
    }
  }, [contractId]);

  // Canvas signature functionality
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (canvas) {
      const rect = canvas.getBoundingClientRect();
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.beginPath();
        ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
      }
    }
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
    const canvas = canvasRef.current;
    if (canvas) {
      const rect = canvas.getBoundingClientRect();
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
        ctx.stroke();
      }
    }
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading contract...</p>
        </div>
      </div>
    );
  }

  if (!contractData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Contract Not Found</h1>
          <p className="text-gray-600">The contract you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  const handleSign = () => {
    if (!clientName.trim()) {
      alert('Please enter your full legal name to proceed.');
      return;
    }

    // Check if either canvas has signature or text signature is provided
    const canvas = canvasRef.current;
    let hasCanvasSignature = false;

    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        hasCanvasSignature = imageData.data.some(channel => channel !== 0);
      }
    }

    if (!hasCanvasSignature && !signature.trim()) {
      alert('Please provide a signature by either drawing in the signature box or typing your name.');
      return;
    }

    setSignedDate(new Date().toISOString().split('T')[0]);
    setIsSigned(true);

    // Save signature data (in real app, this would be sent to server)
    const signatureData = {
      clientName,
      textSignature: signature,
      canvasSignature: hasCanvasSignature ? canvas?.toDataURL() : null,
      signedDate: new Date().toISOString(),
      contractId: contractData.id
    };

    localStorage.setItem(`signature_${contractData.id}`, JSON.stringify(signatureData));

    // In a real app, this would send the signature data to the server
    alert('✅ Contract signed successfully!\n\nThe contractor has been notified of your signature. You will receive a copy via email shortly.');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Modern Header with Company Branding */}
      <div
        className="relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg, ${contractData.primaryColor || '#3b82f6'} 0%, ${contractData.primaryColor || '#3b82f6'}dd 100%)`
        }}
      >
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center text-white">
            {/* Company Logo and Name */}
            <div className="flex justify-center items-center mb-6">
              {contractData.companyLogo ? (
                <img
                  src={contractData.companyLogo}
                  alt="Company Logo"
                  className="h-16 w-16 rounded-full bg-white/20 p-2 mr-4"
                />
              ) : (
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mr-4">
                  <Building className="h-8 w-8 text-white" />
                </div>
              )}
              <div className="text-left">
                <h2 className="text-xl font-bold">{contractData.companyName || 'Construction Company'}</h2>
                <p className="text-white/80 text-sm">Professional Construction Services</p>
              </div>
            </div>

            {/* Contract Title */}
            <h1 className="text-4xl font-bold mb-3">{contractData.title}</h1>
            <p className="text-white/90 text-lg mb-4">Professional Construction Contract</p>

            {/* Contract Details */}
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                Contract ID: {contractData.id}
              </Badge>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                <Calendar className="h-3 w-3 mr-1" />
                Created: {new Date(contractData.createdAt).toLocaleDateString()}
              </Badge>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                <Star className="h-3 w-3 mr-1" />
                Professional Grade
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 -mt-6 relative z-10">

        {/* Status Banner */}
        {isSigned ? (
          <Card className="mb-6 bg-green-50 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="font-semibold text-green-900">Contract Signed Successfully</h3>
                  <p className="text-green-700 text-sm">Signed on {new Date(signedDate).toLocaleDateString()} by {clientName}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="mb-6 bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-blue-900">Awaiting Your Signature</h3>
                  <p className="text-blue-700 text-sm">Please review the contract details below and sign when ready</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Contract Details */}
        <div className="space-y-6">
          {/* Client Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Client Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Client Name</p>
                    <p className="font-semibold">{contractData.clientName}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-semibold">{contractData.clientEmail}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-semibold">{contractData.clientPhone}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Project Address</p>
                    <p className="font-semibold">{contractData.projectAddress}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Project Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Start Date</p>
                    <p className="font-semibold">{new Date(contractData.startDate).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Completion Date</p>
                    <p className="font-semibold">{new Date(contractData.endDate).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Total Amount</p>
                    <p className="font-semibold text-green-600">${parseInt(contractData.totalAmount).toLocaleString()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Project Type</p>
                    <p className="font-semibold capitalize">{contractData.projectType}</p>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h4 className="font-semibold mb-2">Project Description</h4>
                <p className="text-gray-700">{contractData.description}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Scope of Work</h4>
                <p className="text-gray-700">{contractData.scope}</p>
              </div>
            </CardContent>
          </Card>

          {/* Financial Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Financial Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Payment Schedule</h4>
                <p className="text-gray-700">{contractData.paymentSchedule}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Materials & Specifications</h4>
                <p className="text-gray-700">{contractData.materials}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Labor Details</h4>
                <p className="text-gray-700">{contractData.laborDetails}</p>
              </div>
            </CardContent>
          </Card>

          {/* Terms & Warranties */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Terms & Warranties
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Terms & Conditions</h4>
                <p className="text-gray-700">{contractData.terms}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Warranties & Guarantees</h4>
                <p className="text-gray-700">{contractData.warranties}</p>
              </div>
            </CardContent>
          </Card>

          {/* Digital Signature */}
          {!isSigned && (
            <Card className="border-2 border-dashed border-gray-300 bg-gradient-to-br from-white to-gray-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <PenTool className="h-6 w-6" style={{ color: contractData.primaryColor }} />
                  Digital Signature
                </CardTitle>
                <CardDescription className="text-base">
                  By signing below, you agree to all terms and conditions outlined in this contract.
                  You can either draw your signature or type your name.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Client Name */}
                <div className="space-y-2">
                  <Label htmlFor="clientName" className="text-base font-semibold">Full Legal Name *</Label>
                  <Input
                    id="clientName"
                    placeholder="Enter your full legal name"
                    value={clientName}
                    onChange={(e) => setClientName(e.target.value)}
                    className="text-lg p-4 border-2"
                  />
                </div>

                {/* Signature Options */}
                <div className="space-y-4">
                  <Label className="text-base font-semibold">Choose Signature Method:</Label>

                  {/* Canvas Signature */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">Draw Your Signature</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={clearSignature}
                      >
                        Clear
                      </Button>
                    </div>
                    <div className="border-2 border-gray-300 rounded-lg bg-white">
                      <canvas
                        ref={canvasRef}
                        width={400}
                        height={150}
                        className="w-full h-32 cursor-crosshair rounded-lg"
                        onMouseDown={startDrawing}
                        onMouseMove={draw}
                        onMouseUp={stopDrawing}
                        onMouseLeave={stopDrawing}
                        style={{ touchAction: 'none' }}
                      />
                    </div>
                    <p className="text-xs text-gray-500">Draw your signature in the box above</p>
                  </div>

                  {/* OR Divider */}
                  <div className="flex items-center gap-4">
                    <div className="flex-1 h-px bg-gray-300"></div>
                    <span className="text-sm text-gray-500 font-medium">OR</span>
                    <div className="flex-1 h-px bg-gray-300"></div>
                  </div>

                  {/* Typed Signature */}
                  <div className="space-y-2">
                    <Label htmlFor="signature" className="text-sm font-medium">Type Your Signature</Label>
                    <Input
                      id="signature"
                      placeholder="Type your full name as signature"
                      value={signature}
                      onChange={(e) => setSignature(e.target.value)}
                      className="text-lg p-4 border-2 font-serif italic"
                      style={{ fontFamily: 'cursive' }}
                    />
                  </div>
                </div>

                {/* Sign Button */}
                <div className="pt-6">
                  <Button
                    onClick={handleSign}
                    className="w-full text-white text-lg py-6 font-semibold shadow-lg hover:shadow-xl transition-all"
                    size="lg"
                    style={{ backgroundColor: contractData.primaryColor }}
                  >
                    <PenTool className="h-6 w-6 mr-3" />
                    Sign Contract & Accept Terms
                  </Button>
                  <p className="text-xs text-gray-500 text-center mt-2">
                    This action will legally bind you to the contract terms
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Footer */}
        <div className="text-center mt-8 pt-8 border-t">
          <p className="text-gray-500 text-sm">
            This is a legally binding contract. Please read all terms carefully before signing.
          </p>
          <p className="text-gray-400 text-xs mt-2">
            Generated by ConstructionSync Contract Builder
          </p>
        </div>
      </div>
    </div>
  );
};

export default PublicContractView;
