import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  FileText, 
  Plus, 
  Send, 
  Download, 
  Users, 
  Calendar, 
  Settings,
  CheckCircle,
  Building,
  PenTool,
  Briefcase
} from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import { useContractAuth } from "@/contexts/ContractAuthContext";
import ContractDashboardLayout from "@/components/ContractDashboardLayout";

const StandaloneContractBuilder = () => {
  const { user, createContract } = useContractAuth();
  const [contractData, setContractData] = useState({
    title: "",
    type: "",
    template: "",
    clientName: "",
    clientEmail: "",
    clientPhone: "",
    projectScope: "",
    startDate: "",
    completionDate: "",
    totalCost: "",
    materialCost: "",
    laborCost: "",
    depositAmount: "",
    paymentTerms: "",
    warrantyPeriod: "",
    customClauses: "",
    deliverables: "",
    milestones: ""
  });

  const contractTemplates = [
    { 
      id: "fixed-price", 
      name: "Fixed Price Contract", 
      description: "Standard contract with fixed total cost",
      type: "contract",
      category: "General Construction"
    },
    { 
      id: "cost-plus", 
      name: "Cost Plus Contract", 
      description: "Contract with cost reimbursement plus fee",
      type: "contract",
      category: "General Construction"
    },
    { 
      id: "electrical-contract", 
      name: "Electrical Services Contract", 
      description: "Specialized contract for electrical work",
      type: "contract",
      category: "Electrical"
    },
    { 
      id: "hvac-contract", 
      name: "HVAC Installation Contract", 
      description: "Contract for heating and cooling systems",
      type: "contract",
      category: "HVAC"
    },
    { 
      id: "design-contract", 
      name: "Architectural Design Contract", 
      description: "Contract for design and planning services",
      type: "contract",
      category: "Design"
    },
    { 
      id: "renovation-contract", 
      name: "Home Renovation Contract", 
      description: "Contract for residential renovation projects",
      type: "contract",
      category: "Residential"
    }
  ];

  return (
    <ContractDashboardLayout>
      <div className="p-4 sm:p-6 lg:p-8">
        {/* Page Header */}
        <div className="mb-6 lg:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Contract Builder</h1>
          <p className="text-muted-foreground mt-2 text-sm sm:text-base">
            Create professional construction contracts with ease
          </p>
        </div>
        <div className="max-w-6xl mx-auto space-y-6 lg:space-y-8">
          {/* Header */}
          <div className="text-center px-4">
            <Badge className="mb-4 bg-primary/10 text-primary border-primary/20 text-xs sm:text-sm">
              Professional Contract Tool
            </Badge>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
              Contract Builder
            </h1>
            <p className="text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto px-4">
              Create comprehensive, legally-compliant contracts with professional templates, e-signature integration, and client collaboration
            </p>
          </div>

          <Tabs defaultValue="create" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="create">Create Contract</TabsTrigger>
              <TabsTrigger value="templates">Contract Templates</TabsTrigger>
            </TabsList>

            <TabsContent value="create" className="space-y-6">
              {/* Contract Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Create New Contract
                  </CardTitle>
                  <CardDescription>
                    Build a comprehensive contract with legal clauses, payment terms, and project specifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="title">Contract Title</Label>
                      <Input
                        id="title"
                        placeholder="e.g., Office Complex Construction Contract"
                        value={contractData.title}
                        onChange={(e) => setContractData({...contractData, title: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="type">Contract Type</Label>
                      <Select 
                        value={contractData.type}
                        onValueChange={(value) => setContractData({...contractData, type: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select contract type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fixed-price">Fixed Price</SelectItem>
                          <SelectItem value="cost-plus">Cost Plus</SelectItem>
                          <SelectItem value="time-materials">Time & Materials</SelectItem>
                          <SelectItem value="design-build">Design-Build</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="template">Template (Optional)</Label>
                    <Select 
                      value={contractData.template}
                      onValueChange={(value) => setContractData({...contractData, template: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a template to get started" />
                      </SelectTrigger>
                      <SelectContent>
                        {contractTemplates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name} - {template.category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Client Information */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Client Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="clientName">Client Name</Label>
                        <Input
                          id="clientName"
                          placeholder="John Smith / ABC Company"
                          value={contractData.clientName}
                          onChange={(e) => setContractData({...contractData, clientName: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientEmail">Client Email</Label>
                        <Input
                          id="clientEmail"
                          type="email"
                          placeholder="<EMAIL>"
                          value={contractData.clientEmail}
                          onChange={(e) => setContractData({...contractData, clientEmail: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientPhone">Client Phone</Label>
                        <Input
                          id="clientPhone"
                          placeholder="+****************"
                          value={contractData.clientPhone}
                          onChange={(e) => setContractData({...contractData, clientPhone: e.target.value})}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Project Details</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="projectScope">Project Scope</Label>
                        <Textarea
                          id="projectScope"
                          placeholder="Detailed description of work to be performed..."
                          value={contractData.projectScope}
                          onChange={(e) => setContractData({...contractData, projectScope: e.target.value})}
                          rows={4}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="startDate">Start Date</Label>
                          <Input
                            id="startDate"
                            type="date"
                            value={contractData.startDate}
                            onChange={(e) => setContractData({...contractData, startDate: e.target.value})}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="completionDate">Completion Date</Label>
                          <Input
                            id="completionDate"
                            type="date"
                            value={contractData.completionDate}
                            onChange={(e) => setContractData({...contractData, completionDate: e.target.value})}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Cost Breakdown */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Cost Breakdown</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="materialCost">Material Cost ($)</Label>
                        <Input
                          id="materialCost"
                          type="number"
                          placeholder="150000"
                          value={contractData.materialCost}
                          onChange={(e) => setContractData({...contractData, materialCost: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="laborCost">Labor Cost ($)</Label>
                        <Input
                          id="laborCost"
                          type="number"
                          placeholder="100000"
                          value={contractData.laborCost}
                          onChange={(e) => setContractData({...contractData, laborCost: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="totalCost">Total Cost ($)</Label>
                        <Input
                          id="totalCost"
                          type="number"
                          placeholder="250000"
                          value={contractData.totalCost}
                          onChange={(e) => setContractData({...contractData, totalCost: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="deposit">Deposit Required ($)</Label>
                        <Input
                          id="deposit"
                          type="number"
                          placeholder="50000"
                          value={contractData.depositAmount}
                          onChange={(e) => setContractData({...contractData, depositAmount: e.target.value})}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Terms and Conditions */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Terms & Conditions</h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="paymentTerms">Payment Terms</Label>
                          <Textarea
                            id="paymentTerms"
                            placeholder="Define payment milestones, due dates, and conditions..."
                            value={contractData.paymentTerms}
                            onChange={(e) => setContractData({...contractData, paymentTerms: e.target.value})}
                            rows={3}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="warranty">Warranty Period</Label>
                          <Select 
                            value={contractData.warrantyPeriod}
                            onValueChange={(value) => setContractData({...contractData, warrantyPeriod: value})}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select warranty period" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="6-months">6 Months</SelectItem>
                              <SelectItem value="1-year">1 Year</SelectItem>
                              <SelectItem value="2-years">2 Years</SelectItem>
                              <SelectItem value="5-years">5 Years</SelectItem>
                              <SelectItem value="custom">Custom Period</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="clauses">Custom Clauses</Label>
                        <Textarea
                          id="clauses"
                          placeholder="Add warranty terms, delay penalties, or other custom clauses..."
                          value={contractData.customClauses}
                          onChange={(e) => setContractData({...contractData, customClauses: e.target.value})}
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="border-t pt-6">
                    <div className="flex flex-col lg:flex-row justify-between gap-4">
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Button variant="outline">
                          <Download className="mr-2 h-4 w-4" />
                          Preview PDF
                        </Button>
                        <Button variant="outline">
                          Save Draft
                        </Button>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Button variant="outline">
                          <Users className="mr-2 h-4 w-4" />
                          Generate Client Link
                        </Button>
                        <Button>
                          <Send className="mr-2 h-4 w-4" />
                          Send for E-Signature
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2 text-center">
                      Contracts are automatically saved and synced with client portals in real-time
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates" className="space-y-6">
              {/* Templates */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Contract Templates
                  </CardTitle>
                  <CardDescription>
                    Choose from professionally designed templates for different types of construction contracts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {contractTemplates.map((template) => (
                      <Card key={template.id} className="border-2 border-dashed hover:border-primary transition-colors cursor-pointer group">
                        <CardHeader className="px-4 py-3">
                          <div className="flex items-center justify-between mb-2">
                            <Badge variant="outline" className="text-xs">
                              {template.category}
                            </Badge>
                            <Badge variant="default" className="text-xs">
                              Contract
                            </Badge>
                          </div>
                          <CardTitle className="text-base flex items-center">
                            <FileText className="mr-2 h-4 w-4 group-hover:text-primary transition-colors" />
                            <span className="text-sm">{template.name}</span>
                          </CardTitle>
                          <CardDescription className="text-xs">{template.description}</CardDescription>
                        </CardHeader>
                        <CardContent className="pt-0 px-4 pb-4">
                          <Button className="w-full text-sm group-hover:bg-primary group-hover:text-white transition-colors">
                            <Plus className="mr-2 h-4 w-4" />
                            Use Template
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                  <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold mb-2 text-sm">Contract Features:</h4>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      <li>• Pre-filled industry-standard clauses and terms</li>
                      <li>• Legal compliance for construction contracts</li>
                      <li>• E-signature integration and client approval workflow</li>
                      <li>• Real-time synchronization with client portals</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </ContractDashboardLayout>
  );
};

export default StandaloneContractBuilder;
