import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import QuotationForm from '@/components/QuotationForm';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, Loader2 } from 'lucide-react';

interface QuotationData {
  id: string;
  template: any;
  customization: any;
  status: string;
  createdBy: string;
  createdAt: string;
}

const PublicQuotationForm: React.FC = () => {
  const { quotationId } = useParams<{ quotationId: string }>();
  const [quotationData, setQuotationData] = useState<QuotationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchQuotationData = async () => {
      try {
        // Simulate API call to fetch quotation data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock quotation data - in real app, this would come from your backend
        const mockQuotationData: QuotationData = {
          id: quotationId || '',
          template: {
            id: 'modern-minimalist',
            name: 'Modern Minimalist Quotation',
            category: 'quotation',
            description: 'A clean, modern quotation template'
          },
          customization: {
            primaryColor: '#059669',
            secondaryColor: '#6b7280',
            accentColor: '#dc2626',
            backgroundColor: '#ffffff',
            textColor: '#111827',
            companyName: 'Elite Builders Inc.',
            companyAddress: '456 Construction Ave, Builder City, BC 67890',
            companyPhone: '(*************',
            companyEmail: '<EMAIL>',
            companyWebsite: 'www.elitebuilders.com',
            logoUrl: '',
            logoFile: null
          },
          status: 'sent',
          createdBy: '<EMAIL>',
          createdAt: new Date().toISOString()
        };

        setQuotationData(mockQuotationData);
      } catch (err) {
        setError('Failed to load quotation. Please check the link and try again.');
      } finally {
        setLoading(false);
      }
    };

    if (quotationId) {
      fetchQuotationData();
    } else {
      setError('Invalid quotation link.');
      setLoading(false);
    }
  }, [quotationId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-green-600" />
              <h3 className="text-lg font-semibold mb-2">Loading Quotation</h3>
              <p className="text-gray-600">Please wait while we load your quotation...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !quotationData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="w-8 h-8 mx-auto mb-4 text-red-600" />
              <h3 className="text-lg font-semibold mb-2 text-red-800">Quotation Not Found</h3>
              <p className="text-gray-600 mb-4">
                {error || 'The quotation you are looking for could not be found.'}
              </p>
              <p className="text-sm text-gray-500">
                Please check the link or contact the sender for a new link.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <QuotationForm
      quotationId={quotationData.id}
      template={quotationData.template}
      customization={quotationData.customization}
      isReadOnly={false}
    />
  );
};

export default PublicQuotationForm;
