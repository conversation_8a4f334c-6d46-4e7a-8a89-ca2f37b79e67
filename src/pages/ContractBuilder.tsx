
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Plus, Send, Download, Users, Calendar, Settings } from "lucide-react";
import { useState } from "react";
import DashboardLayout from "@/components/DashboardLayout";

const ContractBuilder = () => {
  const [contractData, setContractData] = useState({
    title: "",
    type: "",
    projectScope: "",
    startDate: "",
    completionDate: "",
    totalCost: "",
    depositAmount: "",
    paymentTerms: "",
    customClauses: ""
  });

  // Mock existing contracts/quotations
  const existingDocuments = [
    {
      id: "CNT001",
      title: "Downtown Office Complex - Main Contract",
      type: "contract",
      status: "signed",
      client: "Sterling Corp",
      totalCost: 250000,
      createdDate: "Nov 1, 2025",
      signedDate: "Nov 5, 2025"
    },
    {
      id: "QUO001", 
      title: "Electrical Installation Quote",
      type: "quotation",
      status: "pending-approval",
      client: "Sterling Corp",
      totalCost: 45000,
      createdDate: "Nov 10, 2025",
      signedDate: null
    },
    {
      id: "CNT002",
      title: "Plumbing Services Contract",
      type: "contract", 
      status: "awaiting-signature",
      client: "BuildCorp Inc",
      totalCost: 35000,
      createdDate: "Nov 8, 2025",
      signedDate: null
    }
  ];

  const templates = [
    { id: "fixed-price", name: "Fixed Price Contract", description: "Standard contract with fixed total cost" },
    { id: "cost-plus", name: "Cost Plus Contract", description: "Contract with cost reimbursement plus fee" },
    { id: "service-quote", name: "Service Quotation", description: "Quotation for specific services" },
    { id: "material-quote", name: "Material Quotation", description: "Quotation for material supply" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "signed": return "bg-green-500";
      case "pending-approval": return "bg-yellow-500";
      case "awaiting-signature": return "bg-blue-500";
      default: return "bg-gray-500";
    }
  };

  const getStatusText = (status: string) => {
    return status.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="mb-6 md:mb-8">
          <h1 className="text-2xl md:text-3xl font-bold mb-2">Contract & Quotation Builder</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Create, manage, and share professional contracts and quotations with clients. 
            All documents sync in real-time with client portals and project links.
          </p>
        </div>

        <Tabs defaultValue="create" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 h-auto">
            <TabsTrigger value="create" className="text-xs md:text-sm p-2 md:p-3">Create Document</TabsTrigger>
            <TabsTrigger value="existing" className="text-xs md:text-sm p-2 md:p-3">Existing Documents</TabsTrigger>
            <TabsTrigger value="templates" className="text-xs md:text-sm p-2 md:p-3">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-6">
            <Card>
              <CardHeader className="px-4 md:px-6">
                <CardTitle className="flex items-center text-lg md:text-xl">
                  <FileText className="mr-2 h-4 w-4 md:h-5 md:w-5" />
                  Create New Contract or Quotation
                </CardTitle>
                <CardDescription className="text-sm">
                  Fill in the details below to generate a professional document
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 md:space-y-6 px-4 md:px-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="title" className="text-sm">Document Title</Label>
                    <Input
                      id="title"
                      placeholder="e.g., Office Complex Construction Contract"
                      value={contractData.title}
                      onChange={(e) => setContractData({...contractData, title: e.target.value})}
                      className="text-sm"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type" className="text-sm">Document Type</Label>
                    <Select 
                      value={contractData.type}
                      onValueChange={(value) => setContractData({...contractData, type: value})}
                    >
                      <SelectTrigger className="text-sm">
                        <SelectValue placeholder="Select document type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="quotation">Quotation</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="scope" className="text-sm">Project Scope</Label>
                  <Textarea
                    id="scope"
                    placeholder="Describe the work to be performed, deliverables, and project requirements..."
                    value={contractData.projectScope}
                    onChange={(e) => setContractData({...contractData, projectScope: e.target.value})}
                    rows={4}
                    className="text-sm"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="startDate" className="text-sm">Start Date</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={contractData.startDate}
                      onChange={(e) => setContractData({...contractData, startDate: e.target.value})}
                      className="text-sm"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="completionDate" className="text-sm">Estimated Completion</Label>
                    <Input
                      id="completionDate"
                      type="date"
                      value={contractData.completionDate}
                      onChange={(e) => setContractData({...contractData, completionDate: e.target.value})}
                      className="text-sm"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="totalCost" className="text-sm">Total Cost ($)</Label>
                    <Input
                      id="totalCost"
                      type="number"
                      placeholder="250000"
                      value={contractData.totalCost}
                      onChange={(e) => setContractData({...contractData, totalCost: e.target.value})}
                      className="text-sm"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="deposit" className="text-sm">Deposit Required ($)</Label>
                    <Input
                      id="deposit"
                      type="number"
                      placeholder="50000"
                      value={contractData.depositAmount}
                      onChange={(e) => setContractData({...contractData, depositAmount: e.target.value})}
                      className="text-sm"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="paymentTerms" className="text-sm">Payment Terms</Label>
                  <Textarea
                    id="paymentTerms"
                    placeholder="Define payment milestones, due dates, and conditions..."
                    value={contractData.paymentTerms}
                    onChange={(e) => setContractData({...contractData, paymentTerms: e.target.value})}
                    rows={3}
                    className="text-sm"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clauses" className="text-sm">Custom Clauses</Label>
                  <Textarea
                    id="clauses"
                    placeholder="Add warranty terms, delay penalties, or other custom clauses..."
                    value={contractData.customClauses}
                    onChange={(e) => setContractData({...contractData, customClauses: e.target.value})}
                    rows={3}
                    className="text-sm"
                  />
                </div>

                <div className="flex flex-col md:flex-row justify-between gap-4 pt-4">
                  <Button variant="outline" className="w-full md:w-auto">
                    <Download className="mr-2 h-4 w-4" />
                    Preview PDF
                  </Button>
                  <div className="flex flex-col md:flex-row gap-2 md:space-x-2">
                    <Button variant="outline" className="w-full md:w-auto">Save Draft</Button>
                    <Button className="w-full md:w-auto">
                      <Send className="mr-2 h-4 w-4" />
                      Share with Client
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="existing">
            <Card>
              <CardHeader className="px-4 md:px-6">
                <CardTitle className="text-lg md:text-xl">Existing Contracts & Quotations</CardTitle>
                <CardDescription className="text-sm">
                  Manage and track all your contracts and quotations
                </CardDescription>
              </CardHeader>
              <CardContent className="px-4 md:px-6">
                <div className="space-y-4">
                  {existingDocuments.map((doc) => (
                    <div key={doc.id} className="border rounded-lg p-4">
                      <div className="flex flex-col space-y-3">
                        <div className="flex-1">
                          <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-2 mb-2">
                            <div className="flex items-center space-x-2">
                              <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <h3 className="font-semibold text-sm md:text-base">{doc.title}</h3>
                            </div>
                            <Badge variant="outline" className="text-xs w-fit">
                              {doc.type}
                            </Badge>
                          </div>
                          <p className="text-xs md:text-sm text-muted-foreground mb-1">
                            Client: {doc.client} • Created: {doc.createdDate}
                          </p>
                          <p className="text-sm font-medium">
                            Total: ${doc.totalCost.toLocaleString()}
                          </p>
                        </div>
                        
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                          <Badge className={`${getStatusColor(doc.status)} text-white w-fit text-xs`}>
                            {getStatusText(doc.status)}
                          </Badge>
                          <div className="flex flex-col md:flex-row gap-2">
                            <Button variant="outline" size="sm" className="text-xs">
                              View Details
                            </Button>
                            <Button variant="outline" size="sm" className="text-xs">
                              <Send className="h-3 w-3 md:h-4 md:w-4" />
                              <span className="ml-1 md:ml-0">Share</span>
                            </Button>
                          </div>
                        </div>
                        
                        {doc.signedDate && (
                          <div className="text-xs text-green-600">
                            Signed on {doc.signedDate}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates">
            <Card>
              <CardHeader className="px-4 md:px-6">
                <CardTitle className="text-lg md:text-xl">Document Templates</CardTitle>
                <CardDescription className="text-sm">
                  Choose from pre-built templates to quickly create contracts and quotations
                </CardDescription>
              </CardHeader>
              <CardContent className="px-4 md:px-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {templates.map((template) => (
                    <Card key={template.id} className="border-2 border-dashed hover:border-primary transition-colors cursor-pointer">
                      <CardHeader className="px-4 py-3 md:px-6 md:py-4">
                        <CardTitle className="text-base md:text-lg flex items-center">
                          <FileText className="mr-2 h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                          <span className="text-sm md:text-base">{template.name}</span>
                        </CardTitle>
                        <CardDescription className="text-xs md:text-sm">{template.description}</CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0 px-4 pb-4 md:px-6">
                        <Button className="w-full text-sm">
                          <Plus className="mr-2 h-4 w-4" />
                          Use Template
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ContractBuilder;
