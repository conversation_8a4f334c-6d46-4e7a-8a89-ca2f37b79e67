
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Users, Calendar, Plus, FileText, Search } from "lucide-react";
import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";

const Dashboard = () => {
  const projects = [
    {
      id: "1",
      name: "Downtown Office Complex",
      client: "Sterling Corp",
      progress: 75,
      budget: 250000,
      spent: 185000,
      status: "active",
      dueDate: "Dec 15, 2025",
      lastUpdate: "2 hours ago"
    },
    {
      id: "2", 
      name: "Residential Villa",
      client: "Johnson Family",
      progress: 45,
      budget: 180000,
      spent: 81000,
      status: "active",
      dueDate: "Jan 30, 2026",
      lastUpdate: "1 day ago"
    },
    {
      id: "3",
      name: "Shopping Center Renovation",
      client: "Metro Properties",
      progress: 100,
      budget: 320000,
      spent: 315000,
      status: "completed",
      dueDate: "Nov 1, 2025",
      lastUpdate: "1 week ago"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500";
      case "completed": return "bg-blue-500";
      case "on-hold": return "bg-yellow-500";
      default: return "bg-gray-500";
    }
  };

  const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0);
  const totalSpent = projects.reduce((sum, p) => sum + p.spent, 0);
  const activeProjects = projects.filter(p => p.status === "active").length;

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">Project Dashboard</h1>
            <p className="text-muted-foreground">
              Manage and monitor all your construction projects in real-time
            </p>
          </div>
          <Button asChild className="mt-4 md:mt-0">
            <Link to="/project/new">
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeProjects}</div>
              <p className="text-xs text-muted-foreground">
                +2 from last month
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalBudget / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">
                Across all projects
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalSpent / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">
                {((totalSpent / totalBudget) * 100).toFixed(1)}% of budget
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Progress</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Across active projects
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Projects Table */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList>
            <TabsTrigger value="all">All Projects</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="space-y-6">
            <div className="grid gap-6">
              {projects.map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`}></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>Client: {project.client}</CardDescription>
                        </div>
                      </div>
                      <div className="flex space-x-2 mt-4 md:mt-0">
                        <Badge variant={project.status === "active" ? "default" : "secondary"}>
                          {project.status}
                        </Badge>
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/project/${project.id}`}>
                            <Search className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Progress</span>
                          <span className="font-medium">{project.progress}%</span>
                        </div>
                        <Progress value={project.progress} className="mb-2" />
                        <p className="text-xs text-muted-foreground">Due: {project.dueDate}</p>
                      </div>
                      
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Budget</span>
                          <span className="font-medium">${(project.budget / 1000).toFixed(0)}K</span>
                        </div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Spent</span>
                          <span className={`font-medium ${project.spent > project.budget * 0.9 ? 'text-red-600' : 'text-green-600'}`}>
                            ${(project.spent / 1000).toFixed(0)}K
                          </span>
                        </div>
                        <Progress 
                          value={Math.min((project.spent / project.budget) * 100, 100)} 
                          className="mb-2"
                        />
                        <p className="text-xs text-muted-foreground">
                          {((project.budget - project.spent) / 1000).toFixed(0)}K remaining
                        </p>
                      </div>
                      
                      <div className="flex flex-col justify-between">
                        <div>
                          <p className="text-sm font-medium mb-1">Last Update</p>
                          <p className="text-xs text-muted-foreground">{project.lastUpdate}</p>
                        </div>
                        <div className="flex space-x-2 mt-4">
                          <Button variant="outline" size="sm" asChild>
                            <Link to={`/client/${project.id}-demo`}>
                              Share Client Link
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="active">
            <div className="grid gap-6">
              {projects.filter(p => p.status === "active").map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  {/* Same card content as above */}
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>Client: {project.client}</CardDescription>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link to={`/project/${project.id}`}>View Details</Link>
                      </Button>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="completed">
            <div className="grid gap-6">
              {projects.filter(p => p.status === "completed").map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>Client: {project.client}</CardDescription>
                        </div>
                      </div>
                      <Badge variant="secondary">Completed</Badge>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
