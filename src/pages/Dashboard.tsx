
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Settings, Users, Calendar, Plus, FileText, Search, MessageCircle, Share2, Eye, AlertCircle, CheckCircle, Clock } from "lucide-react";
import { Link } from "react-router-dom";
import DashboardLayout from "@/components/DashboardLayout";
import { ClientComment, createClientComment } from "@/types/project";

const Dashboard = () => {
  const projects = [
    {
      id: "1",
      name: "Downtown Office Complex",
      client: "Sterling Corp",
      progress: 75,
      budget: 250000,
      spent: 185000,
      status: "active",
      dueDate: "Dec 15, 2025",
      lastUpdate: "2 hours ago",
      clientPortalEnabled: true,
      clientPortalToken: "1-client-abc123",
      unreadComments: 2
    },
    {
      id: "2",
      name: "Residential Villa",
      client: "Johnson Family",
      progress: 45,
      budget: 180000,
      spent: 81000,
      status: "active",
      dueDate: "Jan 30, 2026",
      lastUpdate: "1 day ago",
      clientPortalEnabled: true,
      clientPortalToken: "2-client-def456",
      unreadComments: 0
    },
    {
      id: "3",
      name: "Shopping Center Renovation",
      client: "Metro Properties",
      progress: 100,
      budget: 320000,
      spent: 315000,
      status: "completed",
      dueDate: "Nov 1, 2025",
      lastUpdate: "1 week ago",
      clientPortalEnabled: false,
      clientPortalToken: "",
      unreadComments: 0
    }
  ];

  // Mock client comments data
  const allClientComments: ClientComment[] = [
    {
      ...createClientComment(
        "1",
        "The progress looks great! I'm particularly impressed with the quality of the foundation work. When do you expect to start the interior work?",
        "John Sterling",
        "<EMAIL>",
        "question",
        "medium"
      ),
      status: "new"
    },
    {
      ...createClientComment(
        "1",
        "I have some concerns about the timeline. The weather delays seem to be impacting our original schedule. Can we discuss potential mitigation strategies?",
        "John Sterling",
        "<EMAIL>",
        "concern",
        "high"
      ),
      status: "new"
    },
    {
      ...createClientComment(
        "2",
        "Thank you for the detailed progress report. The villa is looking exactly as we envisioned!",
        "Sarah Johnson",
        "<EMAIL>",
        "general",
        "low"
      ),
      status: "responded"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500";
      case "completed": return "bg-blue-500";
      case "on-hold": return "bg-yellow-500";
      default: return "bg-gray-500";
    }
  };

  const getCommentStatusColor = (status: ClientComment['status']) => {
    switch (status) {
      case 'new': return 'bg-red-500';
      case 'read': return 'bg-yellow-500';
      case 'responded': return 'bg-blue-500';
      case 'resolved': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getCommentsByProject = (projectId: string) => {
    return allClientComments.filter(comment => comment.projectId === projectId);
  };

  const getUnreadCommentsCount = () => {
    return allClientComments.filter(comment => comment.status === 'new').length;
  };

  const getHighPriorityCommentsCount = () => {
    return allClientComments.filter(comment => comment.priority === 'high' && comment.status !== 'resolved').length;
  };

  const generateClientPortalLink = (token: string) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/client/${token}`;
  };

  const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0);
  const totalSpent = projects.reduce((sum, p) => sum + p.spent, 0);
  const activeProjects = projects.filter(p => p.status === "active").length;

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">Project Dashboard</h1>
            <p className="text-muted-foreground">
              Manage and monitor all your construction projects in real-time
            </p>
          </div>
          <Button asChild className="mt-4 md:mt-0">
            <Link to="/project/new">
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeProjects}</div>
              <p className="text-xs text-muted-foreground">
                +2 from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalBudget / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">
                Across all projects
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalSpent / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">
                {((totalSpent / totalBudget) * 100).toFixed(1)}% of budget
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Client Comments</CardTitle>
              <MessageCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{getUnreadCommentsCount()}</div>
              <p className="text-xs text-muted-foreground">
                {getHighPriorityCommentsCount()} high priority
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Progress</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Across active projects
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Consolidated Overview Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Materials Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Materials Overview
              </CardTitle>
              <CardDescription>
                Material status across all projects
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Steel Reinforcement</span>
                  <Badge variant="default">In Stock</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Concrete Mix</span>
                  <Badge variant="destructive">Low Stock</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Electrical Components</span>
                  <Badge variant="secondary">Ordered</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Plumbing Fixtures</span>
                  <Badge variant="default">In Stock</Badge>
                </div>
              </div>
              <div className="pt-2 border-t">
                <div className="text-sm text-muted-foreground">
                  Total Material Cost: <span className="font-medium">$125K</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Labor Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5" />
                Labor Overview
              </CardTitle>
              <CardDescription>
                Workforce status and productivity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Active Workers</span>
                  <span className="font-medium">24</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Hours This Week</span>
                  <span className="font-medium">960</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Efficiency Rate</span>
                  <Badge variant="default">94%</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Overtime Hours</span>
                  <span className="font-medium">48</span>
                </div>
              </div>
              <div className="pt-2 border-t">
                <div className="text-sm text-muted-foreground">
                  Labor Cost This Month: <span className="font-medium">$85K</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cost Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Cost Analysis
              </CardTitle>
              <CardDescription>
                Budget performance overview
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Budget Utilization</span>
                  <span className="font-medium">{((totalSpent / totalBudget) * 100).toFixed(1)}%</span>
                </div>
                <Progress value={(totalSpent / totalBudget) * 100} className="h-2" />
                <div className="flex justify-between items-center">
                  <span className="text-sm">Projects On Budget</span>
                  <Badge variant="default">2 of 3</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Cost Variance</span>
                  <span className="font-medium text-green-600">-2.1%</span>
                </div>
              </div>
              <div className="pt-2 border-t">
                <div className="text-sm text-muted-foreground">
                  Projected Savings: <span className="font-medium text-green-600">$15K</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Client Portal Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Share2 className="mr-2 h-5 w-5" />
              Client Portal Management
            </CardTitle>
            <CardDescription>
              Manage client access, view feedback, and respond to comments across all projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="comments" className="relative">
                  Comments
                  {getUnreadCommentsCount() > 0 && (
                    <Badge className="ml-2 h-5 w-5 p-0 text-xs bg-red-500">
                      {getUnreadCommentsCount()}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="portals">Portal Links</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-2 border-blue-500/20">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center">
                        <Eye className="mr-2 h-4 w-4" />
                        Active Portals
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">
                        {projects.filter(p => p.clientPortalEnabled).length}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        of {projects.length} total projects
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="border-2 border-orange-500/20">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center">
                        <AlertCircle className="mr-2 h-4 w-4" />
                        Pending Comments
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-orange-600">
                        {getUnreadCommentsCount()}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {getHighPriorityCommentsCount()} high priority
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="border-2 border-green-500/20">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Resolved Today
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {allClientComments.filter(c => c.status === 'resolved').length}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        comments resolved
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="comments" className="space-y-4">
                {allClientComments.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No client comments yet</h3>
                    <p className="text-muted-foreground">
                      Client comments and feedback will appear here when submitted through project portals.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {allClientComments.map((comment) => (
                      <Card key={comment.id} className="relative">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="font-medium">{comment.clientName}</span>
                                <Badge variant="outline" className="text-xs">
                                  {projects.find(p => p.id === comment.projectId)?.name}
                                </Badge>
                              </div>
                              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                <span>{new Date(comment.timestamp).toLocaleDateString()}</span>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge className={`${comment.priority === 'high' ? 'bg-red-500' : comment.priority === 'medium' ? 'bg-yellow-500' : 'bg-gray-500'} text-white text-xs`}>
                                {comment.priority}
                              </Badge>
                              <Badge className={`${getCommentStatusColor(comment.status)} text-white text-xs`}>
                                {comment.status}
                              </Badge>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm mb-3">{comment.content}</p>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline" asChild>
                              <Link to={`/project/${comment.projectId}`}>
                                View Project
                              </Link>
                            </Button>
                            {comment.status === 'new' && (
                              <Button size="sm">
                                Respond
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="portals" className="space-y-4">
                <div className="space-y-4">
                  {projects.map((project) => (
                    <Card key={project.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-lg">{project.name}</CardTitle>
                            <CardDescription>Client: {project.client}</CardDescription>
                          </div>
                          <div className="flex items-center space-x-2">
                            {project.clientPortalEnabled ? (
                              <Badge className="bg-green-500 text-white">Active</Badge>
                            ) : (
                              <Badge variant="outline">Disabled</Badge>
                            )}
                            {project.unreadComments > 0 && (
                              <Badge className="bg-red-500 text-white">
                                {project.unreadComments} new
                              </Badge>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {project.clientPortalEnabled ? (
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium">Portal Link:</span>
                              <code className="text-xs bg-muted px-2 py-1 rounded">
                                {generateClientPortalLink(project.clientPortalToken)}
                              </code>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button size="sm" variant="outline">
                                Copy Link
                              </Button>
                              <Button size="sm" variant="outline" asChild>
                                <Link to={`/client/${project.clientPortalToken}`} target="_blank">
                                  <Eye className="mr-2 h-3 w-3" />
                                  Preview
                                </Link>
                              </Button>
                              <Button size="sm" variant="outline" asChild>
                                <Link to={`/project/${project.id}`}>
                                  Manage
                                </Link>
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">
                              Client portal is disabled for this project
                            </span>
                            <Button size="sm" asChild>
                              <Link to={`/project/${project.id}`}>
                                Enable Portal
                              </Link>
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Projects Table */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList>
            <TabsTrigger value="all">All Projects</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="space-y-6">
            <div className="grid gap-6">
              {projects.map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`}></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>Client: {project.client}</CardDescription>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
                        <Badge variant={project.status === "active" ? "default" : "secondary"}>
                          {project.status}
                        </Badge>
                        {project.clientPortalEnabled && (
                          <Badge className="bg-blue-500 text-white">
                            <Share2 className="mr-1 h-3 w-3" />
                            Portal Active
                          </Badge>
                        )}
                        {project.unreadComments > 0 && (
                          <Badge className="bg-red-500 text-white">
                            <MessageCircle className="mr-1 h-3 w-3" />
                            {project.unreadComments} new
                          </Badge>
                        )}
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/project/${project.id}`}>
                            <Search className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Progress</span>
                          <span className="font-medium">{project.progress}%</span>
                        </div>
                        <Progress value={project.progress} className="mb-2" />
                        <p className="text-xs text-muted-foreground">Due: {project.dueDate}</p>
                      </div>
                      
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Budget</span>
                          <span className="font-medium">${(project.budget / 1000).toFixed(0)}K</span>
                        </div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Spent</span>
                          <span className={`font-medium ${project.spent > project.budget * 0.9 ? 'text-red-600' : 'text-green-600'}`}>
                            ${(project.spent / 1000).toFixed(0)}K
                          </span>
                        </div>
                        <Progress 
                          value={Math.min((project.spent / project.budget) * 100, 100)} 
                          className="mb-2"
                        />
                        <p className="text-xs text-muted-foreground">
                          {((project.budget - project.spent) / 1000).toFixed(0)}K remaining
                        </p>
                      </div>
                      
                      <div className="flex flex-col justify-between">
                        <div>
                          <p className="text-sm font-medium mb-1">Last Update</p>
                          <p className="text-xs text-muted-foreground">{project.lastUpdate}</p>
                        </div>
                        <div className="flex space-x-2 mt-4">
                          <Button variant="outline" size="sm" asChild>
                            <Link to={`/client/${project.id}-demo`}>
                              Share Client Link
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="active">
            <div className="grid gap-6">
              {projects.filter(p => p.status === "active").map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>Client: {project.client}</CardDescription>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link to={`/project/${project.id}`}>View Details</Link>
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Progress</span>
                          <span className="font-medium">{project.progress}%</span>
                        </div>
                        <Progress value={project.progress} className="mb-2" />
                        <p className="text-xs text-muted-foreground">Due: {project.dueDate}</p>
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Budget</span>
                          <span className="font-medium">${(project.budget / 1000).toFixed(0)}K</span>
                        </div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Spent</span>
                          <span className={`font-medium ${project.spent > project.budget * 0.9 ? 'text-red-600' : 'text-green-600'}`}>
                            ${(project.spent / 1000).toFixed(0)}K
                          </span>
                        </div>
                        <Progress
                          value={Math.min((project.spent / project.budget) * 100, 100)}
                          className="mb-2"
                        />
                        <p className="text-xs text-muted-foreground">
                          {((project.budget - project.spent) / 1000).toFixed(0)}K remaining
                        </p>
                      </div>
                      <div className="flex flex-col justify-between">
                        <div>
                          <p className="text-sm font-medium mb-1">Last Update</p>
                          <p className="text-xs text-muted-foreground">{project.lastUpdate}</p>
                        </div>
                        <div className="flex space-x-2 mt-4">
                          <Button variant="outline" size="sm" asChild>
                            <Link to={`/client/${project.id}-demo`}>
                              Share Client Link
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="completed">
            <div className="grid gap-6">
              {projects.filter(p => p.status === "completed").map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>Client: {project.client}</CardDescription>
                        </div>
                      </div>
                      <Badge variant="secondary">Completed</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Progress</span>
                          <span className="font-medium">{project.progress}%</span>
                        </div>
                        <Progress value={project.progress} className="mb-2" />
                        <p className="text-xs text-muted-foreground">Completed: {project.dueDate}</p>
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Final Budget</span>
                          <span className="font-medium">${(project.budget / 1000).toFixed(0)}K</span>
                        </div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Total Spent</span>
                          <span className={`font-medium ${project.spent > project.budget ? 'text-red-600' : 'text-green-600'}`}>
                            ${(project.spent / 1000).toFixed(0)}K
                          </span>
                        </div>
                        <Progress
                          value={Math.min((project.spent / project.budget) * 100, 100)}
                          className="mb-2"
                        />
                        <p className="text-xs text-muted-foreground">
                          {project.spent > project.budget ? 'Over budget' : 'Under budget'}
                        </p>
                      </div>
                      <div className="flex flex-col justify-between">
                        <div>
                          <p className="text-sm font-medium mb-1">Completion Date</p>
                          <p className="text-xs text-muted-foreground">{project.lastUpdate}</p>
                        </div>
                        <div className="flex space-x-2 mt-4">
                          <Button variant="outline" size="sm" asChild>
                            <Link to={`/client/${project.id}-demo`}>
                              View Final Report
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
