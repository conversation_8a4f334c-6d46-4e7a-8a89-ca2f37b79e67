import React, { useState } from 'react';
import QuotationDashboardLayout from '@/components/QuotationDashboardLayout';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  Plus,
  Send,
  CheckCircle,
  Building,
  ArrowRight,
  ArrowLeft,
  Clock,
  Edit,
  Eye,
  Share,
  Calculator,
  Trash2,
  PlusCircle
} from "lucide-react";


interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface QuotationData {
  id?: string;
  title: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  projectType: string;
  projectAddress: string;
  validUntil: string;
  lineItems: LineItem[];
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  description: string;
  terms: string;
  notes: string;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired';
  createdAt: string;
  shareableLink?: string;
  // Customization options
  primaryColor?: string;
  companyName?: string;
  companyLogo?: string;
}

const NewQuotationBuilder = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [quotationData, setQuotationData] = useState<QuotationData>({
    title: "",
    clientName: "",
    clientEmail: "",
    clientPhone: "",
    projectType: "",
    projectAddress: "",
    validUntil: "",
    lineItems: [
      { id: '1', description: '', quantity: 1, unitPrice: 0, total: 0 }
    ],
    subtotal: 0,
    taxRate: 8.5,
    taxAmount: 0,
    totalAmount: 0,
    description: "",
    terms: "",
    notes: "",
    status: 'draft',
    createdAt: new Date().toISOString(),
    primaryColor: "#3b82f6",
    companyName: "",
    companyLogo: ""
  });

  const handleInputChange = (field: keyof QuotationData, value: string | number) => {
    setQuotationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addLineItem = () => {
    const newItem: LineItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0
    };
    setQuotationData(prev => ({
      ...prev,
      lineItems: [...prev.lineItems, newItem]
    }));
  };

  const removeLineItem = (id: string) => {
    setQuotationData(prev => ({
      ...prev,
      lineItems: prev.lineItems.filter(item => item.id !== id)
    }));
    calculateTotals();
  };

  const updateLineItem = (id: string, field: keyof LineItem, value: string | number) => {
    setQuotationData(prev => {
      const updatedItems = prev.lineItems.map(item => {
        if (item.id === id) {
          const updated = { ...item, [field]: value };
          if (field === 'quantity' || field === 'unitPrice') {
            updated.total = updated.quantity * updated.unitPrice;
          }
          return updated;
        }
        return item;
      });
      return { ...prev, lineItems: updatedItems };
    });
    calculateTotals();
  };

  const calculateTotals = () => {
    setTimeout(() => {
      setQuotationData(prev => {
        const subtotal = prev.lineItems.reduce((sum, item) => sum + item.total, 0);
        const taxAmount = subtotal * (prev.taxRate / 100);
        const totalAmount = subtotal + taxAmount;
        
        return {
          ...prev,
          subtotal,
          taxAmount,
          totalAmount
        };
      });
    }, 0);
  };

  const generateShareableLink = () => {
    const quotationId = `quotation-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const shareableLink = `${window.location.origin}/quotation/${quotationId}`;
    
    setQuotationData(prev => ({
      ...prev,
      id: quotationId,
      shareableLink: shareableLink,
      status: 'sent'
    }));

    // Copy to clipboard
    navigator.clipboard.writeText(shareableLink).then(() => {
      alert(`✅ Quotation Created Successfully!\n\n📋 Link copied to clipboard:\n${shareableLink}\n\n📧 Share this link with your client to review and accept the quotation.`);
    });
  };

  const handleCreateQuotation = () => {
    if (!quotationData.title || !quotationData.clientName || !quotationData.clientEmail) {
      alert('Please fill in all required fields (Title, Client Name, Client Email)');
      return;
    }
    
    if (quotationData.lineItems.length === 0 || quotationData.lineItems.every(item => !item.description)) {
      alert('Please add at least one line item with a description');
      return;
    }
    
    generateShareableLink();
  };

  const nextStep = () => {
    if (currentStep < 4) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const getStepProgress = () => {
    return (currentStep / 6) * 100;
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const logoDataUrl = e.target?.result as string;
        setQuotationData(prev => ({
          ...prev,
          companyLogo: logoDataUrl
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  // Using shared template data

  const recentQuotations: QuotationData[] = [
    {
      id: "quotation-1",
      title: "Kitchen Renovation Quote",
      clientName: "John Smith",
      clientEmail: "<EMAIL>",
      clientPhone: "(*************",
      projectType: "renovation",
      projectAddress: "123 Main St, City, State",
      validUntil: "2024-02-15",
      lineItems: [
        { id: '1', description: 'Kitchen Cabinets', quantity: 1, unitPrice: 15000, total: 15000 },
        { id: '2', description: 'Countertops', quantity: 1, unitPrice: 5000, total: 5000 },
        { id: '3', description: 'Labor', quantity: 1, unitPrice: 8000, total: 8000 }
      ],
      subtotal: 28000,
      taxRate: 8.5,
      taxAmount: 2380,
      totalAmount: 30380,
      status: "accepted",
      createdAt: "2024-01-15",
      description: "Complete kitchen renovation",
      terms: "50% deposit required",
      notes: "Premium materials included",
      shareableLink: `${window.location.origin}/quotation/quotation-1`
    },
    {
      id: "quotation-2",
      title: "Bathroom Remodel Quote",
      clientName: "Jane Doe",
      clientEmail: "<EMAIL>",
      clientPhone: "(*************",
      projectType: "renovation",
      projectAddress: "456 Oak Ave, City, State",
      validUntil: "2024-02-20",
      lineItems: [
        { id: '1', description: 'Bathroom Fixtures', quantity: 1, unitPrice: 8000, total: 8000 },
        { id: '2', description: 'Tile Work', quantity: 1, unitPrice: 4000, total: 4000 },
        { id: '3', description: 'Labor', quantity: 1, unitPrice: 6000, total: 6000 }
      ],
      subtotal: 18000,
      taxRate: 8.5,
      taxAmount: 1530,
      totalAmount: 19530,
      status: "sent",
      createdAt: "2024-01-10",
      description: "Master bathroom remodel",
      terms: "Payment due within 30 days",
      notes: "High-end finishes",
      shareableLink: `${window.location.origin}/quotation/quotation-2`
    }
  ];

  const getStatusBadge = (status: string) => {
    const styles = {
      accepted: "bg-green-100 text-green-800 border-green-300",
      sent: "bg-blue-100 text-blue-800 border-blue-300",
      draft: "bg-gray-100 text-gray-800 border-gray-300",
      rejected: "bg-red-100 text-red-800 border-red-300",
      expired: "bg-orange-100 text-orange-800 border-orange-300"
    };
    return styles[status as keyof typeof styles] || styles.draft;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted': return <CheckCircle className="h-4 w-4" />;
      case 'sent': return <Send className="h-4 w-4" />;
      case 'draft': return <Edit className="h-4 w-4" />;
      case 'rejected': return <Clock className="h-4 w-4" />;
      case 'expired': return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <QuotationDashboardLayout>
      <div className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="mb-6 lg:mb-8">
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Quotation Builder</h1>
          <p className="text-gray-600">Create professional quotations with dynamic pricing and client approval workflow</p>
        </div>

        <Tabs defaultValue="create" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Quotation
            </TabsTrigger>
            <TabsTrigger value="recent" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Recent Quotations
            </TabsTrigger>
          </TabsList>

          {/* Create Quotation Tab */}
          <TabsContent value="create" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Calculator className="h-5 w-5" />
                      New Quotation
                    </CardTitle>
                    <CardDescription>
                      Step {currentStep} of 6 - Create a professional quotation with dynamic pricing
                    </CardDescription>
                  </div>
                  <Badge variant="outline" className="text-sm">
                    {Math.round(getStepProgress())}% Complete
                  </Badge>
                </div>
                <Progress value={getStepProgress()} className="w-full mt-4" />
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Step 1: Basic Information */}
                {currentStep === 1 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Quotation Title *</Label>
                        <Input
                          id="title"
                          placeholder="e.g., Kitchen Renovation Quote"
                          value={quotationData.title}
                          onChange={(e) => handleInputChange('title', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="projectType">Project Type</Label>
                        <Select value={quotationData.projectType} onValueChange={(value) => handleInputChange('projectType', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="renovation">Renovation</SelectItem>
                            <SelectItem value="new-construction">New Construction</SelectItem>
                            <SelectItem value="maintenance">Maintenance</SelectItem>
                            <SelectItem value="repair">Repair</SelectItem>
                            <SelectItem value="design">Design Services</SelectItem>
                            <SelectItem value="consultation">Consultation</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="validUntil">Valid Until</Label>
                        <Input
                          id="validUntil"
                          type="date"
                          value={quotationData.validUntil}
                          onChange={(e) => handleInputChange('validUntil', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="taxRate">Tax Rate (%)</Label>
                        <Input
                          id="taxRate"
                          type="number"
                          step="0.1"
                          placeholder="8.5"
                          value={quotationData.taxRate}
                          onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Project Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Describe the project scope and objectives..."
                        value={quotationData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        rows={3}
                      />
                    </div>
                  </div>
                )}

                {/* Step 2: Client Information */}
                {currentStep === 2 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Client Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="clientName">Client Name *</Label>
                        <Input
                          id="clientName"
                          placeholder="Full name or company name"
                          value={quotationData.clientName}
                          onChange={(e) => handleInputChange('clientName', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientEmail">Client Email *</Label>
                        <Input
                          id="clientEmail"
                          type="email"
                          placeholder="<EMAIL>"
                          value={quotationData.clientEmail}
                          onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientPhone">Client Phone</Label>
                        <Input
                          id="clientPhone"
                          placeholder="(*************"
                          value={quotationData.clientPhone}
                          onChange={(e) => handleInputChange('clientPhone', e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="projectAddress">Project Address</Label>
                      <Textarea
                        id="projectAddress"
                        placeholder="Full project address including city, state, and zip code"
                        value={quotationData.projectAddress}
                        onChange={(e) => handleInputChange('projectAddress', e.target.value)}
                        rows={2}
                      />
                    </div>
                  </div>
                )}

                {/* Step 3: Line Items & Pricing */}
                {currentStep === 3 && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">Line Items & Pricing</h3>
                      <Button onClick={addLineItem} variant="outline" size="sm" className="flex items-center gap-2">
                        <PlusCircle className="h-4 w-4" />
                        Add Item
                      </Button>
                    </div>

                    <div className="space-y-3">
                      {quotationData.lineItems.map((item, index) => (
                        <Card key={item.id} className="p-4">
                          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                            <div className="md:col-span-2 space-y-2">
                              <Label>Description</Label>
                              <Input
                                placeholder="Item description"
                                value={item.description}
                                onChange={(e) => updateLineItem(item.id, 'description', e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Quantity</Label>
                              <Input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => updateLineItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Unit Price ($)</Label>
                              <Input
                                type="number"
                                step="0.01"
                                value={item.unitPrice}
                                onChange={(e) => updateLineItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                              />
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="flex-1">
                                <Label>Total</Label>
                                <div className="text-lg font-semibold text-green-600">
                                  ${item.total.toFixed(2)}
                                </div>
                              </div>
                              {quotationData.lineItems.length > 1 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeLineItem(item.id)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>

                    {/* Pricing Summary */}
                    <Card className="bg-gray-50">
                      <CardContent className="p-4">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span>Subtotal:</span>
                            <span className="font-semibold">${quotationData.subtotal.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Tax ({quotationData.taxRate}%):</span>
                            <span className="font-semibold">${quotationData.taxAmount.toFixed(2)}</span>
                          </div>
                          <div className="border-t pt-2 flex justify-between text-lg font-bold text-green-600">
                            <span>Total:</span>
                            <span>${quotationData.totalAmount.toFixed(2)}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Step 4: Terms & Notes */}
                {currentStep === 4 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Terms & Notes</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="terms">Terms & Conditions</Label>
                        <Textarea
                          id="terms"
                          placeholder="Payment terms, conditions, and legal clauses..."
                          value={quotationData.terms}
                          onChange={(e) => handleInputChange('terms', e.target.value)}
                          rows={3}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="notes">Additional Notes</Label>
                        <Textarea
                          id="notes"
                          placeholder="Any additional information for the client..."
                          value={quotationData.notes}
                          onChange={(e) => handleInputChange('notes', e.target.value)}
                          rows={2}
                        />
                      </div>
                    </div>

                    {/* Final Summary */}
                    <Card className="bg-blue-50 border-blue-200">
                      <CardContent className="p-4">
                        <h4 className="font-semibold text-blue-900 mb-2">Quotation Summary</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p><strong>Client:</strong> {quotationData.clientName || 'Not specified'}</p>
                            <p><strong>Project:</strong> {quotationData.title || 'Not specified'}</p>
                            <p><strong>Valid Until:</strong> {quotationData.validUntil || 'Not specified'}</p>
                          </div>
                          <div>
                            <p><strong>Items:</strong> {quotationData.lineItems.length}</p>
                            <p><strong>Subtotal:</strong> ${quotationData.subtotal.toFixed(2)}</p>
                            <p><strong>Total:</strong> <span className="text-lg font-bold text-green-600">${quotationData.totalAmount.toFixed(2)}</span></p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Step 5: Customization */}
                {currentStep === 5 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Customize Your Quotation</h3>

                    {/* Color Customization */}
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="primaryColor">Primary Color</Label>
                        <div className="flex items-center gap-4">
                          <input
                            type="color"
                            id="primaryColor"
                            value={quotationData.primaryColor}
                            onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                            className="w-16 h-10 rounded border border-gray-300 cursor-pointer"
                          />
                          <Input
                            value={quotationData.primaryColor}
                            onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                            placeholder="#3b82f6"
                            className="flex-1"
                          />
                        </div>
                      </div>

                      {/* Company Name */}
                      <div className="space-y-2">
                        <Label htmlFor="companyName">Company Name</Label>
                        <Input
                          id="companyName"
                          value={quotationData.companyName}
                          onChange={(e) => handleInputChange('companyName', e.target.value)}
                          placeholder="Your Company Name"
                        />
                      </div>

                      {/* Logo Upload */}
                      <div className="space-y-2">
                        <Label htmlFor="logo">Company Logo</Label>
                        <div className="flex items-center gap-4">
                          <input
                            type="file"
                            id="logo"
                            accept="image/*"
                            onChange={handleLogoUpload}
                            className="hidden"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById('logo')?.click()}
                            className="flex items-center gap-2"
                          >
                            <Building className="h-4 w-4" />
                            Upload Logo
                          </Button>
                          {quotationData.companyLogo && (
                            <div className="flex items-center gap-2">
                              <img
                                src={quotationData.companyLogo}
                                alt="Company Logo"
                                className="w-12 h-12 object-contain border rounded"
                              />
                              <span className="text-sm text-green-600">✓ Logo uploaded</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 6: Preview */}
                {currentStep === 6 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Preview Your Quotation</h3>

                    <div className="border rounded-lg p-6 bg-gray-50" style={{ borderColor: quotationData.primaryColor }}>
                      {/* Header with logo and company name */}
                      <div className="flex items-center justify-between mb-6 pb-4 border-b" style={{ borderColor: quotationData.primaryColor }}>
                        <div className="flex items-center gap-4">
                          {quotationData.companyLogo && (
                            <img
                              src={quotationData.companyLogo}
                              alt="Company Logo"
                              className="w-16 h-16 object-contain"
                            />
                          )}
                          <div>
                            <h2 className="text-2xl font-bold" style={{ color: quotationData.primaryColor }}>
                              {quotationData.companyName || "Your Company"}
                            </h2>
                            <p className="text-gray-600">Project Quotation</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">Quote Date</p>
                          <p className="font-semibold">{new Date().toLocaleDateString()}</p>
                        </div>
                      </div>

                      {/* Quotation Details Preview */}
                      <div className="space-y-4">
                        <div>
                          <h3 className="font-semibold text-lg mb-2" style={{ color: quotationData.primaryColor }}>
                            {quotationData.title}
                          </h3>
                          <p className="text-gray-700">{quotationData.description}</p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-semibold mb-2">Client Information</h4>
                            <p><strong>Name:</strong> {quotationData.clientName}</p>
                            <p><strong>Email:</strong> {quotationData.clientEmail}</p>
                            <p><strong>Phone:</strong> {quotationData.clientPhone}</p>
                          </div>
                          <div>
                            <h4 className="font-semibold mb-2">Project Details</h4>
                            <p><strong>Type:</strong> {quotationData.projectType}</p>
                            <p><strong>Address:</strong> {quotationData.projectAddress}</p>
                            <p><strong>Valid Until:</strong> {quotationData.validUntil}</p>
                          </div>
                        </div>

                        {/* Line Items Preview */}
                        <div>
                          <h4 className="font-semibold mb-2">Items & Pricing</h4>
                          <div className="bg-white rounded border">
                            <table className="w-full">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="text-left p-3 border-b">Description</th>
                                  <th className="text-center p-3 border-b">Qty</th>
                                  <th className="text-right p-3 border-b">Unit Price</th>
                                  <th className="text-right p-3 border-b">Total</th>
                                </tr>
                              </thead>
                              <tbody>
                                {quotationData.lineItems.map((item, index) => (
                                  <tr key={index} className="border-b">
                                    <td className="p-3">{item.description}</td>
                                    <td className="p-3 text-center">{item.quantity}</td>
                                    <td className="p-3 text-right">${item.unitPrice.toFixed(2)}</td>
                                    <td className="p-3 text-right">${item.total.toFixed(2)}</td>
                                  </tr>
                                ))}
                              </tbody>
                              <tfoot className="bg-gray-50">
                                <tr>
                                  <td colSpan={3} className="p-3 text-right font-semibold">Subtotal:</td>
                                  <td className="p-3 text-right">${quotationData.subtotal.toFixed(2)}</td>
                                </tr>
                                <tr>
                                  <td colSpan={3} className="p-3 text-right font-semibold">Tax ({quotationData.taxRate}%):</td>
                                  <td className="p-3 text-right">${quotationData.taxAmount.toFixed(2)}</td>
                                </tr>
                                <tr>
                                  <td colSpan={3} className="p-3 text-right font-bold text-lg" style={{ color: quotationData.primaryColor }}>Total:</td>
                                  <td className="p-3 text-right font-bold text-lg" style={{ color: quotationData.primaryColor }}>${quotationData.totalAmount.toFixed(2)}</td>
                                </tr>
                              </tfoot>
                            </table>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 5: Customization */}
                {currentStep === 5 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Customize Your Quotation</h3>

                    {/* Color Customization */}
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="primaryColor">Primary Color</Label>
                        <div className="flex items-center gap-4">
                          <input
                            type="color"
                            id="primaryColor"
                            value={quotationData.primaryColor}
                            onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                            className="w-16 h-10 rounded border border-gray-300 cursor-pointer"
                          />
                          <Input
                            value={quotationData.primaryColor}
                            onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                            placeholder="#3b82f6"
                            className="flex-1"
                          />
                        </div>
                      </div>

                      {/* Company Name */}
                      <div className="space-y-2">
                        <Label htmlFor="companyName">Company Name</Label>
                        <Input
                          id="companyName"
                          value={quotationData.companyName}
                          onChange={(e) => handleInputChange('companyName', e.target.value)}
                          placeholder="Your Company Name"
                        />
                      </div>

                      {/* Logo Upload */}
                      <div className="space-y-2">
                        <Label htmlFor="logo">Company Logo</Label>
                        <div className="flex items-center gap-4">
                          <input
                            type="file"
                            id="logo"
                            accept="image/*"
                            onChange={handleLogoUpload}
                            className="hidden"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById('logo')?.click()}
                            className="flex items-center gap-2"
                          >
                            <Building className="h-4 w-4" />
                            Upload Logo
                          </Button>
                          {quotationData.companyLogo && (
                            <div className="flex items-center gap-2">
                              <img
                                src={quotationData.companyLogo}
                                alt="Company Logo"
                                className="w-12 h-12 object-contain border rounded"
                              />
                              <span className="text-sm text-green-600">✓ Logo uploaded</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 6: Preview */}
                {currentStep === 6 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Preview Your Quotation</h3>

                    <div className="border rounded-lg p-6 bg-gray-50" style={{ borderColor: quotationData.primaryColor }}>
                      {/* Header with logo and company name */}
                      <div className="flex items-center justify-between mb-6 pb-4 border-b" style={{ borderColor: quotationData.primaryColor }}>
                        <div className="flex items-center gap-4">
                          {quotationData.companyLogo && (
                            <img
                              src={quotationData.companyLogo}
                              alt="Company Logo"
                              className="w-16 h-16 object-contain"
                            />
                          )}
                          <div>
                            <h2 className="text-2xl font-bold" style={{ color: quotationData.primaryColor }}>
                              {quotationData.companyName || "Your Company"}
                            </h2>
                            <p className="text-gray-600">Project Quotation</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">Quote Date</p>
                          <p className="font-semibold">{new Date().toLocaleDateString()}</p>
                        </div>
                      </div>

                      {/* Quotation Details Preview */}
                      <div className="space-y-4">
                        <div>
                          <h3 className="font-semibold text-lg mb-2" style={{ color: quotationData.primaryColor }}>
                            {quotationData.title}
                          </h3>
                          <p className="text-gray-700">{quotationData.description}</p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-semibold mb-2">Client Information</h4>
                            <p><strong>Name:</strong> {quotationData.clientName}</p>
                            <p><strong>Email:</strong> {quotationData.clientEmail}</p>
                            <p><strong>Phone:</strong> {quotationData.clientPhone}</p>
                          </div>
                          <div>
                            <h4 className="font-semibold mb-2">Project Details</h4>
                            <p><strong>Type:</strong> {quotationData.projectType}</p>
                            <p><strong>Address:</strong> {quotationData.projectAddress}</p>
                            <p><strong>Valid Until:</strong> {quotationData.validUntil}</p>
                          </div>
                        </div>

                        {/* Line Items Preview */}
                        <div>
                          <h4 className="font-semibold mb-2">Items & Pricing</h4>
                          <div className="space-y-2">
                            {quotationData.lineItems.map((item, index) => (
                              <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200">
                                <div>
                                  <p className="font-medium">{item.description}</p>
                                  <p className="text-sm text-gray-500">Qty: {item.quantity} × ${item.unitPrice}</p>
                                </div>
                                <p className="font-semibold">${item.total.toFixed(2)}</p>
                              </div>
                            ))}
                          </div>

                          <div className="mt-4 pt-4 border-t border-gray-300">
                            <div className="flex justify-between text-lg font-bold" style={{ color: quotationData.primaryColor }}>
                              <span>Total Amount:</span>
                              <span>${quotationData.totalAmount.toFixed(2)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6 border-t">
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Previous
                  </Button>

                  {currentStep < 6 ? (
                    <Button onClick={nextStep} className="flex items-center gap-2">
                      {currentStep === 5 ? 'Preview' : 'Next'}
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button onClick={handleCreateQuotation} className="flex items-center gap-2 bg-green-600 hover:bg-green-700">
                      <Send className="h-4 w-4" />
                      Create & Share Quotation
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>



          {/* Recent Quotations Tab */}
          <TabsContent value="recent" className="space-y-6">
            <div className="space-y-4">
              {recentQuotations.map((quotation) => (
                <Card key={quotation.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Calculator className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{quotation.title}</h3>
                          <p className="text-gray-600">Client: {quotation.clientName}</p>
                          <p className="text-sm text-gray-500">Created: {new Date(quotation.createdAt).toLocaleDateString()}</p>
                          <p className="text-sm text-gray-500">Valid Until: {new Date(quotation.validUntil).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="font-semibold text-lg">${quotation.totalAmount.toLocaleString()}</p>
                          <p className="text-sm text-gray-500">{quotation.lineItems.length} items</p>
                          <Badge className={getStatusBadge(quotation.status)}>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(quotation.status)}
                              {quotation.status.charAt(0).toUpperCase() + quotation.status.slice(1)}
                            </div>
                          </Badge>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (quotation.shareableLink) {
                                navigator.clipboard.writeText(quotation.shareableLink).then(() => {
                                  alert(`📋 Quotation link copied to clipboard:\n${quotation.shareableLink}`);
                                });
                              }
                            }}
                          >
                            <Share className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </QuotationDashboardLayout>
  );
};

export default NewQuotationBuilder;
