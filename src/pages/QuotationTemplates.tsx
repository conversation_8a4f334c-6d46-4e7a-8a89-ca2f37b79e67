import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Calculator, 
  Search, 
  Filter,
  Eye,
  Download,
  Star,
  Building,
  Home,
  Wrench,
  Palette,
  DollarSign,
  Zap,
  TrendingUp
} from "lucide-react";
import QuotationDashboardLayout from "@/components/QuotationDashboardLayout";
import TemplatePreviewModal from "@/components/TemplatePreviewModal";
import TemplateCustomizationModal from "@/components/TemplateCustomizationModal";

interface QuotationTemplate {
  id: string;
  name: string;
  description: string;
  category: "classic" | "modern" | "creative" | "detailed";
  type: "residential" | "commercial" | "renovation" | "maintenance" | "design" | "landscaping";
  rating: number;
  downloads: number;
  preview: string;
  features: string[];
  color: string;
  icon: any;
}

const QuotationTemplates = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [previewTemplate, setPreviewTemplate] = useState<QuotationTemplate | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [customizeTemplate, setCustomizeTemplate] = useState<QuotationTemplate | null>(null);
  const [isCustomizeOpen, setIsCustomizeOpen] = useState(false);

  const templates: QuotationTemplate[] = [
    // Classic Professional Templates
    {
      id: "classic-residential-quote-1",
      name: "Classic Residential Quote",
      description: "Traditional, detailed residential construction quotation with itemized breakdown",
      category: "classic",
      type: "residential",
      rating: 4.9,
      downloads: 1567,
      preview: "Professional layout with detailed line items and traditional formatting",
      features: ["Detailed material breakdown", "Labor cost analysis", "Tax calculations", "Payment terms"],
      color: "bg-slate-50 border-slate-200",
      icon: Home
    },
    {
      id: "classic-commercial-quote-1",
      name: "Classic Commercial Estimate",
      description: "Comprehensive commercial construction quotation with extensive cost analysis",
      category: "classic",
      type: "commercial",
      rating: 4.8,
      downloads: 1234,
      preview: "Formal business quotation with detailed project phases and cost breakdowns",
      features: ["Multi-phase pricing", "Subcontractor costs", "Equipment rental", "Contingency planning"],
      color: "bg-slate-50 border-slate-200",
      icon: Building
    },
    {
      id: "classic-renovation-quote-1",
      name: "Classic Renovation Quote",
      description: "Time-tested renovation quotation template with proven cost structure",
      category: "classic",
      type: "renovation",
      rating: 4.7,
      downloads: 892,
      preview: "Traditional renovation quote with detailed scope and pricing breakdown",
      features: ["Demolition costs", "Material specifications", "Permit fees", "Cleanup services"],
      color: "bg-slate-50 border-slate-200",
      icon: Wrench
    },

    // Modern Minimalist Templates
    {
      id: "modern-residential-quote-1",
      name: "Modern Residential Estimate",
      description: "Clean, contemporary quotation design with streamlined pricing presentation",
      category: "modern",
      type: "residential",
      rating: 4.9,
      downloads: 1789,
      preview: "Sleek, modern layout with visual cost breakdowns and clean typography",
      features: ["Visual cost charts", "Digital acceptance", "Real-time pricing", "Mobile-friendly"],
      color: "bg-orange-50 border-orange-200",
      icon: Home
    },
    {
      id: "modern-commercial-quote-1",
      name: "Modern Commercial Proposal",
      description: "Contemporary commercial quotation with innovative pricing visualization",
      category: "modern",
      type: "commercial",
      rating: 4.8,
      downloads: 1456,
      preview: "Modern business proposal with interactive elements and clear pricing structure",
      features: ["Interactive pricing", "Progress visualization", "Digital signatures", "Cloud integration"],
      color: "bg-orange-50 border-orange-200",
      icon: Building
    },
    {
      id: "modern-design-quote-1",
      name: "Modern Design-Build Quote",
      description: "Integrated design-build quotation with contemporary project pricing methods",
      category: "modern",
      type: "design",
      rating: 4.9,
      downloads: 1123,
      preview: "Streamlined design-build quote with modern pricing presentation",
      features: ["Design integration", "Value engineering", "3D visualization", "Collaborative pricing"],
      color: "bg-orange-50 border-orange-200",
      icon: Palette
    },

    // Creative Contemporary Templates
    {
      id: "creative-residential-quote-1",
      name: "Creative Custom Home Quote",
      description: "Innovative quotation design for unique residential projects with artistic pricing",
      category: "creative",
      type: "residential",
      rating: 4.7,
      downloads: 678,
      preview: "Creative layout with visual elements and modern typography for unique projects",
      features: ["Custom design pricing", "Artistic specifications", "Unique materials", "Creative timelines"],
      color: "bg-purple-50 border-purple-200",
      icon: Home
    },
    {
      id: "creative-landscaping-quote-1",
      name: "Creative Landscaping Quote",
      description: "Artistic landscaping quotation for outdoor spaces and garden transformations",
      category: "creative",
      type: "landscaping",
      rating: 4.6,
      downloads: 534,
      preview: "Visually appealing quote design for landscaping and outdoor projects",
      features: ["Plant specifications", "Seasonal pricing", "Maintenance plans", "Design visualization"],
      color: "bg-purple-50 border-purple-200",
      icon: Palette
    },

    // Detailed Analysis Templates
    {
      id: "detailed-commercial-quote-1",
      name: "Detailed Commercial Analysis",
      description: "Comprehensive commercial quotation with in-depth cost analysis and projections",
      category: "detailed",
      type: "commercial",
      rating: 4.9,
      downloads: 2345,
      preview: "Detailed analytical quotation with comprehensive cost breakdowns and projections",
      features: ["Cost analysis", "ROI projections", "Risk assessment", "Market comparisons"],
      color: "bg-green-50 border-green-200",
      icon: TrendingUp
    },
    {
      id: "detailed-maintenance-quote-1",
      name: "Detailed Maintenance Agreement",
      description: "Comprehensive maintenance quotation with ongoing service pricing",
      category: "detailed",
      type: "maintenance",
      rating: 4.8,
      downloads: 1234,
      preview: "Detailed maintenance quote with service level pricing and annual projections",
      features: ["Service tiers", "Annual pricing", "Emergency rates", "Performance metrics"],
      color: "bg-green-50 border-green-200",
      icon: Wrench
    },
    {
      id: "detailed-renovation-quote-1",
      name: "Detailed Renovation Analysis",
      description: "In-depth renovation quotation with comprehensive cost and timeline analysis",
      category: "detailed",
      type: "renovation",
      rating: 4.8,
      downloads: 987,
      preview: "Comprehensive renovation quote with detailed analysis and cost projections",
      features: ["Phase-by-phase pricing", "Timeline analysis", "Cost comparisons", "Alternative options"],
      color: "bg-green-50 border-green-200",
      icon: Wrench
    }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory;
    const matchesType = selectedType === "all" || template.type === selectedType;
    
    return matchesSearch && matchesCategory && matchesType;
  });

  const getCategoryBadge = (category: string) => {
    const styles = {
      classic: "bg-slate-100 text-slate-800 border-slate-300",
      modern: "bg-orange-100 text-orange-800 border-orange-300",
      creative: "bg-purple-100 text-purple-800 border-purple-300",
      detailed: "bg-green-100 text-green-800 border-green-300"
    };
    return styles[category as keyof typeof styles] || styles.classic;
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      residential: Home,
      commercial: Building,
      renovation: Wrench,
      maintenance: Wrench,
      design: Palette,
      landscaping: Palette
    };
    return icons[type as keyof typeof icons] || Calculator;
  };

  const handlePreviewTemplate = (template: QuotationTemplate) => {
    setPreviewTemplate(template);
    setIsPreviewOpen(true);
  };

  const handleUseTemplate = (templateId: string) => {
    // Here you would typically navigate to the quotation builder with the selected template
    console.log("Using template:", templateId);
    setIsPreviewOpen(false);
    // You could navigate to the quotation builder page with the template pre-loaded
    // navigate(`/quotations?template=${templateId}`);
  };

  const handleCustomizeTemplate = (template: QuotationTemplate) => {
    setCustomizeTemplate(template);
    setIsCustomizeOpen(true);
    setIsPreviewOpen(false);
  };

  const handleSaveCustomization = (templateId: string, customization: any) => {
    console.log("Saving customization for template:", templateId, customization);
    // Here you would save the customization and navigate to the quotation builder
    // with the customized template
    // navigate(`/quotations?template=${templateId}&customization=${JSON.stringify(customization)}`);
  };

  return (
    <QuotationDashboardLayout>
      <div className="p-4 sm:p-6 lg:p-8">
        {/* Page Header */}
        <div className="mb-6 lg:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Quotation Templates</h1>
          <p className="text-muted-foreground mt-2 text-sm sm:text-base">
            Choose from professionally designed quotation templates for your construction projects
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="classic">Classic Professional</SelectItem>
                <SelectItem value="modern">Modern Minimalist</SelectItem>
                <SelectItem value="creative">Creative Contemporary</SelectItem>
                <SelectItem value="detailed">Detailed Analysis</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Project Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="residential">Residential</SelectItem>
                <SelectItem value="commercial">Commercial</SelectItem>
                <SelectItem value="renovation">Renovation</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="design">Design-Build</SelectItem>
                <SelectItem value="landscaping">Landscaping</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => {
            const IconComponent = template.icon;
            const TypeIcon = getTypeIcon(template.type);
            
            return (
              <Card key={template.id} className={`${template.color} hover:shadow-lg transition-all duration-300 group`}>
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className={`p-2 rounded-lg ${
                        template.category === 'classic' ? 'bg-slate-200' :
                        template.category === 'modern' ? 'bg-orange-200' :
                        template.category === 'creative' ? 'bg-purple-200' :
                        'bg-green-200'
                      }`}>
                        <IconComponent className="h-5 w-5" />
                      </div>
                      <TypeIcon className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <Badge className={getCategoryBadge(template.category)}>
                      {template.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg group-hover:text-accent transition-colors">
                    {template.name}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {template.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>{template.rating}</span>
                    </div>
                    <span>{template.downloads} downloads</span>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Key Features:</p>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {template.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <div className="w-1 h-1 bg-current rounded-full" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="grid grid-cols-3 gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreviewTemplate(template)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Preview
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCustomizeTemplate(template)}
                    >
                      <Palette className="h-4 w-4 mr-1" />
                      Customize
                    </Button>
                    <Button
                      size="sm"
                      className="bg-accent hover:bg-accent/90"
                      onClick={() => handleUseTemplate(template.id)}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Use
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <Calculator className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-muted-foreground">Try adjusting your search criteria or filters</p>
          </div>
        )}

        {/* Template Preview Modal */}
        <TemplatePreviewModal
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          template={previewTemplate}
          onUseTemplate={handleUseTemplate}
          onCustomizeTemplate={handleCustomizeTemplate}
        />

        {/* Template Customization Modal */}
        <TemplateCustomizationModal
          isOpen={isCustomizeOpen}
          onClose={() => setIsCustomizeOpen(false)}
          template={customizeTemplate}
          onSaveCustomization={handleSaveCustomization}
        />
      </div>
    </QuotationDashboardLayout>
  );
};

export default QuotationTemplates;
