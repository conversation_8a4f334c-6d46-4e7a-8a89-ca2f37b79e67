import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Calculator,
  Search,
  Eye,
  Download,
  Building,
  Palette,
  Zap,
  Shield
} from "lucide-react";
import QuotationDashboardLayout from "@/components/QuotationDashboardLayout";
import TemplatePreviewModal from "@/components/TemplatePreviewModal";
import TemplateCustomizationModal from "@/components/TemplateCustomizationModal";
import { quotationWebTemplates, WebTemplate } from "@/data/webTemplates";

// Using WebTemplate interface from webTemplates.ts

const QuotationTemplates = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [previewTemplate, setPreviewTemplate] = useState<WebTemplate | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [customizeTemplate, setCustomizeTemplate] = useState<WebTemplate | null>(null);
  const [isCustomizeOpen, setIsCustomizeOpen] = useState(false);

  const templates: WebTemplate[] = quotationWebTemplates;



  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const getCategoryBadge = (category: string) => {
    const styles = {
      classic: "bg-slate-100 text-slate-800 border-slate-300",
      modern: "bg-blue-100 text-blue-800 border-blue-300",
      creative: "bg-purple-100 text-purple-800 border-purple-300",
      minimal: "bg-gray-100 text-gray-800 border-gray-300",
      professional: "bg-green-100 text-green-800 border-green-300"
    };
    return styles[category as keyof typeof styles] || styles.modern;
  };

  const handlePreviewTemplate = (template: WebTemplate) => {
    setPreviewTemplate(template);
    setIsPreviewOpen(true);
  };

  const handleUseTemplate = (templateId: string) => {
    // Generate a unique quotation ID and create shareable link
    const quotationId = `quotation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const shareableLink = `${window.location.origin}/quotation/${quotationId}`;

    // In a real app, you would save the quotation data to your backend here
    console.log("Generated shareable quotation link:", shareableLink);

    // Copy link to clipboard and show success message
    navigator.clipboard.writeText(shareableLink).then(() => {
      alert(`Quotation link generated and copied to clipboard!\n\nShare this link with your client:\n${shareableLink}`);
    });

    setIsPreviewOpen(false);
  };

  const handleCustomizeTemplate = (template: WebTemplate) => {
    setCustomizeTemplate(template);
    setIsCustomizeOpen(true);
    setIsPreviewOpen(false);
  };

  const handleSaveCustomization = (templateId: string, customization: any) => {
    // Generate a unique quotation ID with customization
    const quotationId = `quotation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const shareableLink = `${window.location.origin}/quotation/${quotationId}`;

    // In a real app, you would save the customized quotation data to your backend here
    console.log("Saving customization and generating link:", { customization, quotationId });

    // Copy link to clipboard and show success message
    navigator.clipboard.writeText(shareableLink).then(() => {
      alert(`Customized quotation link generated and copied to clipboard!\n\nShare this link with your client:\n${shareableLink}`);
    });

    setIsCustomizeOpen(false);
  };

  return (
    <QuotationDashboardLayout>
      <div className="p-4 sm:p-6 lg:p-8">
        {/* Page Header */}
        <div className="mb-6 lg:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Quotation Templates</h1>
          <p className="text-muted-foreground mt-2 text-sm sm:text-base">
            Choose from professionally designed quotation templates for your construction projects
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="classic">Classic</SelectItem>
                <SelectItem value="modern">Modern</SelectItem>
                <SelectItem value="minimal">Minimal</SelectItem>
                <SelectItem value="creative">Creative</SelectItem>
                <SelectItem value="professional">Professional</SelectItem>
              </SelectContent>
            </Select>

          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => {
            const getCardColor = (category: string) => {
              const colors = {
                classic: "bg-slate-50 border-slate-200",
                modern: "bg-blue-50 border-blue-200",
                creative: "bg-purple-50 border-purple-200",
                minimal: "bg-gray-50 border-gray-200",
                professional: "bg-green-50 border-green-200"
              };
              return colors[category as keyof typeof colors] || colors.modern;
            };

            const getIconColor = (category: string) => {
              const colors = {
                classic: "bg-slate-200 text-slate-700",
                modern: "bg-blue-200 text-blue-700",
                creative: "bg-purple-200 text-purple-700",
                minimal: "bg-gray-200 text-gray-700",
                professional: "bg-green-200 text-green-700"
              };
              return colors[category as keyof typeof colors] || colors.modern;
            };

            return (
              <Card key={template.id} className={`${getCardColor(template.category)} hover:shadow-lg transition-all duration-300 group`}>
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className={`p-2 rounded-lg ${getIconColor(template.category)}`}>
                        <Calculator className="h-5 w-5" />
                      </div>
                      {template.isInteractive && <Zap className="h-4 w-4 text-yellow-500" />}
                      {template.hasSignature && <Shield className="h-4 w-4 text-green-500" />}
                      {template.mobileOptimized && <Building className="h-4 w-4 text-blue-500" />}
                    </div>
                    <Badge className={getCategoryBadge(template.category)}>
                      {template.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg group-hover:text-accent transition-colors">
                    {template.name}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {template.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      {template.isInteractive && (
                        <span className="flex items-center space-x-1">
                          <Zap className="h-3 w-3" />
                          <span className="text-xs">Interactive</span>
                        </span>
                      )}
                      {template.hasSignature && (
                        <span className="flex items-center space-x-1">
                          <Shield className="h-3 w-3" />
                          <span className="text-xs">Signature</span>
                        </span>
                      )}
                    </div>
                    <span className="text-xs">{template.type}</span>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium">Key Features:</p>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {template.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <div className="w-1 h-1 bg-current rounded-full" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="grid grid-cols-3 gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreviewTemplate(template)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Preview
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCustomizeTemplate(template)}
                    >
                      <Palette className="h-4 w-4 mr-1" />
                      Customize
                    </Button>
                    <Button
                      size="sm"
                      className="bg-accent hover:bg-accent/90"
                      onClick={() => handleUseTemplate(template.id)}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Use
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <Calculator className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-muted-foreground">Try adjusting your search criteria or filters</p>
          </div>
        )}

        {/* Template Preview Modal */}
        <TemplatePreviewModal
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          template={previewTemplate}
          onUseTemplate={handleUseTemplate}
          onCustomizeTemplate={handleCustomizeTemplate}
        />

        {/* Template Customization Modal */}
        <TemplateCustomizationModal
          isOpen={isCustomizeOpen}
          onClose={() => setIsCustomizeOpen(false)}
          template={customizeTemplate}
          onSaveCustomization={handleSaveCustomization}
        />
      </div>
    </QuotationDashboardLayout>
  );
};

export default QuotationTemplates;
