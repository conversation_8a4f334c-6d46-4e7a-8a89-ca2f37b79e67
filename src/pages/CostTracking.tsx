
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Plus, Calendar, Users, FileText } from "lucide-react";
import DashboardLayout from "@/components/DashboardLayout";

const CostTracking = () => {
  const projectCosts = [
    {
      id: "1",
      project: "Downtown Office Complex",
      totalBudget: 250000,
      spent: 185000,
      categories: {
        materials: 85000,
        labor: 75000,
        equipment: 15000,
        permits: 8000,
        other: 2000
      },
      status: "on-track"
    },
    {
      id: "2",
      project: "Residential Villa",
      totalBudget: 180000,
      spent: 81000,
      categories: {
        materials: 45000,
        labor: 28000,
        equipment: 5000,
        permits: 2000,
        other: 1000
      },
      status: "under-budget"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "on-track": return "bg-green-500";
      case "under-budget": return "bg-blue-500";
      case "over-budget": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };

  const totalBudget = projectCosts.reduce((sum, p) => sum + p.totalBudget, 0);
  const totalSpent = projectCosts.reduce((sum, p) => sum + p.spent, 0);

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Cost Tracking & Budget Management</h1>
            <p className="text-muted-foreground">
              Monitor expenses, track budgets, and forecast costs across all projects
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Expense
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalBudget / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">
                Across all active projects
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalSpent / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">
                {((totalSpent / totalBudget) * 100).toFixed(1)}% of total budget
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Remaining</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ${((totalBudget - totalSpent) / 1000).toFixed(0)}K
              </div>
              <p className="text-xs text-muted-foreground">
                Available across projects
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget Health</CardTitle>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">Healthy</div>
              <p className="text-xs text-muted-foreground">
                All projects on track
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Project Cost Breakdown */}
        <div className="space-y-6">
          {projectCosts.map((project) => {
            const budgetPercentage = (project.spent / project.totalBudget) * 100;
            const remaining = project.totalBudget - project.spent;
            
            return (
              <Card key={project.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl">{project.project}</CardTitle>
                      <CardDescription>
                        Budget: ${(project.totalBudget / 1000).toFixed(0)}K • 
                        Spent: ${(project.spent / 1000).toFixed(0)}K • 
                        Remaining: ${(remaining / 1000).toFixed(0)}K
                      </CardDescription>
                    </div>
                    <Badge variant="outline" className={`${getStatusColor(project.status)} text-white`}>
                      {project.status.replace('-', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid lg:grid-cols-2 gap-8">
                    {/* Budget Progress */}
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Budget Usage</span>
                        <span className="font-medium">{budgetPercentage.toFixed(1)}%</span>
                      </div>
                      <Progress value={budgetPercentage} className="mb-6" />
                      
                      <div className="space-y-3">
                        <h4 className="font-semibold text-sm">Cost Breakdown</h4>
                        {Object.entries(project.categories).map(([category, amount]) => (
                          <div key={category} className="flex justify-between items-center">
                            <span className="text-sm capitalize">{category}</span>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium">
                                ${(amount / 1000).toFixed(0)}K
                              </span>
                              <div className="w-16 bg-muted rounded-full h-1.5">
                                <div 
                                  className="bg-primary h-1.5 rounded-full"
                                  style={{ width: `${(amount / project.spent) * 100}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Recent Expenses */}
                    <div>
                      <h4 className="font-semibold text-sm mb-3">Recent Expenses</h4>
                      <div className="space-y-3">
                        <div className="border-l-2 border-blue-200 pl-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Steel reinforcement bars</span>
                            <span className="text-sm font-medium text-blue-600">$12,000</span>
                          </div>
                          <p className="text-xs text-muted-foreground">Materials • 2 days ago</p>
                        </div>
                        <div className="border-l-2 border-green-200 pl-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Electrical contractor</span>
                            <span className="text-sm font-medium text-green-600">$8,500</span>
                          </div>
                          <p className="text-xs text-muted-foreground">Labor • 3 days ago</p>
                        </div>
                        <div className="border-l-2 border-orange-200 pl-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Crane rental</span>
                            <span className="text-sm font-medium text-orange-600">$2,400</span>
                          </div>
                          <p className="text-xs text-muted-foreground">Equipment • 5 days ago</p>
                        </div>
                      </div>
                      
                      <Button variant="outline" size="sm" className="w-full mt-4">
                        View All Expenses
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Cost Forecasting */}
        <Card className="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardHeader>
            <CardTitle>Cost Forecasting & Alerts</CardTitle>
            <CardDescription>
              AI-powered insights and budget predictions based on current spending patterns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 mb-1">3 days</div>
                <p className="text-sm text-muted-foreground">Ahead of schedule</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">$15K</div>
                <p className="text-sm text-muted-foreground">Projected savings</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 mb-1">0</div>
                <p className="text-sm text-muted-foreground">Budget alerts</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default CostTracking;
