import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import ContractForm from '@/components/ContractForm';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, Loader2 } from 'lucide-react';

interface ContractData {
  id: string;
  template: any;
  customization: any;
  status: string;
  createdBy: string;
  createdAt: string;
}

const PublicContractForm: React.FC = () => {
  const { contractId } = useParams<{ contractId: string }>();
  const [contractData, setContractData] = useState<ContractData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContractData = async () => {
      try {
        // Simulate API call to fetch contract data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock contract data - in real app, this would come from your backend
        const mockContractData: ContractData = {
          id: contractId || '',
          template: {
            id: 'classic-professional',
            name: 'Classic Professional Contract',
            category: 'contract',
            description: 'A traditional, professional contract template'
          },
          customization: {
            primaryColor: '#2563eb',
            secondaryColor: '#64748b',
            accentColor: '#f59e0b',
            backgroundColor: '#ffffff',
            textColor: '#1f2937',
            companyName: 'ABC Construction Co.',
            companyAddress: '123 Builder Street, Construction City, CC 12345',
            companyPhone: '(*************',
            companyEmail: '<EMAIL>',
            companyWebsite: 'www.abcconstruction.com',
            logoUrl: '',
            logoFile: null
          },
          status: 'sent',
          createdBy: '<EMAIL>',
          createdAt: new Date().toISOString()
        };

        setContractData(mockContractData);
      } catch (err) {
        setError('Failed to load contract. Please check the link and try again.');
      } finally {
        setLoading(false);
      }
    };

    if (contractId) {
      fetchContractData();
    } else {
      setError('Invalid contract link.');
      setLoading(false);
    }
  }, [contractId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
              <h3 className="text-lg font-semibold mb-2">Loading Contract</h3>
              <p className="text-gray-600">Please wait while we load your contract...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !contractData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="w-8 h-8 mx-auto mb-4 text-red-600" />
              <h3 className="text-lg font-semibold mb-2 text-red-800">Contract Not Found</h3>
              <p className="text-gray-600 mb-4">
                {error || 'The contract you are looking for could not be found.'}
              </p>
              <p className="text-sm text-gray-500">
                Please check the link or contact the sender for a new link.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <ContractForm
      contractId={contractData.id}
      template={contractData.template}
      customization={contractData.customization}
      isReadOnly={false}
    />
  );
};

export default PublicContractForm;
