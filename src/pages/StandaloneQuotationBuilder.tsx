import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Calculator, 
  Plus, 
  Send, 
  Download, 
  Users, 
  Trash2,
  Settings,
  FileText,
  DollarSign
} from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  total: number;
}

const StandaloneQuotationBuilder = () => {
  const [clientName, setClientName] = useState("");
  const [clientEmail, setClientEmail] = useState("");
  const [quotationTitle, setQuotationTitle] = useState("");
  const [quotationType, setQuotationType] = useState("");
  const [lineItems, setLineItems] = useState<LineItem[]>([
    { id: "1", description: "", quantity: 1, unit: "unit", unitPrice: 0, total: 0 }
  ]);

  const addLineItem = () => {
    const newItem: LineItem = {
      id: Date.now().toString(),
      description: "",
      quantity: 1,
      unit: "unit",
      unitPrice: 0,
      total: 0
    };
    setLineItems([...lineItems, newItem]);
  };

  const removeLineItem = (id: string) => {
    setLineItems(lineItems.filter(item => item.id !== id));
  };

  const updateLineItem = (id: string, field: keyof LineItem, value: string | number) => {
    setLineItems(lineItems.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
        }
        return updatedItem;
      }
      return item;
    }));
  };

  const calculateSubtotal = () => {
    return lineItems.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTax = (subtotal: number) => {
    return subtotal * 0.1; // 10% tax
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax(subtotal);
    return subtotal + tax;
  };

  const quotationTemplates = [
    { 
      id: "material-supply", 
      name: "Material Supply Quote", 
      description: "Quote for construction materials and supplies",
      category: "Materials"
    },
    { 
      id: "labor-services", 
      name: "Labor Services Quote", 
      description: "Quote for labor and installation services",
      category: "Services"
    },
    { 
      id: "electrical-quote", 
      name: "Electrical Work Quote", 
      description: "Specialized quote for electrical installations",
      category: "Electrical"
    },
    { 
      id: "plumbing-quote", 
      name: "Plumbing Services Quote", 
      description: "Quote for plumbing work and fixtures",
      category: "Plumbing"
    },
    { 
      id: "hvac-quote", 
      name: "HVAC Installation Quote", 
      description: "Quote for heating and cooling systems",
      category: "HVAC"
    },
    { 
      id: "design-quote", 
      name: "Design Services Quote", 
      description: "Quote for architectural and design services",
      category: "Design"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link to="/professional-selection" className="flex items-center space-x-2">
              <div className="h-8 w-8 construction-gradient rounded-lg flex items-center justify-center">
                <Settings className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                ConstructionSync
              </h1>
            </Link>
            <div className="flex items-center space-x-4">
              <Link to="/contracts" className="text-muted-foreground hover:text-primary transition-colors">
                Contract Builder
              </Link>
              <Link to="/quotation-login" className="text-muted-foreground hover:text-accent transition-colors">
                Sign In
              </Link>
              <Button variant="outline" asChild>
                <Link to="/quotation-register">Create Account</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center">
            <Badge className="mb-4 bg-accent/10 text-accent border-accent/20">
              Professional Quotation Tool
            </Badge>
            <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-accent to-orange-500 bg-clip-text text-transparent">
              Quotation Builder
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Create detailed, professional quotations with automatic calculations, line-item breakdowns, and instant client sharing
            </p>
          </div>

          <Tabs defaultValue="create" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="create">Create Quotation</TabsTrigger>
              <TabsTrigger value="templates">Quotation Templates</TabsTrigger>
            </TabsList>

            <TabsContent value="create" className="space-y-6">
              {/* Quotation Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calculator className="mr-2 h-5 w-5" />
                    Create New Quotation
                  </CardTitle>
                  <CardDescription>
                    Build a detailed quotation with line items, automatic calculations, and professional formatting
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="quotationTitle">Quotation Title</Label>
                      <Input
                        id="quotationTitle"
                        placeholder="e.g., Kitchen Renovation Quote"
                        value={quotationTitle}
                        onChange={(e) => setQuotationTitle(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="quotationType">Quotation Type</Label>
                      <Select value={quotationType} onValueChange={setQuotationType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select quotation type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="materials">Materials Supply</SelectItem>
                          <SelectItem value="labor">Labor Services</SelectItem>
                          <SelectItem value="electrical">Electrical Work</SelectItem>
                          <SelectItem value="plumbing">Plumbing Services</SelectItem>
                          <SelectItem value="hvac">HVAC Installation</SelectItem>
                          <SelectItem value="design">Design Services</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Client Information */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Client Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="clientName">Client Name</Label>
                        <Input
                          id="clientName"
                          placeholder="John Smith / ABC Company"
                          value={clientName}
                          onChange={(e) => setClientName(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientEmail">Client Email</Label>
                        <Input
                          id="clientEmail"
                          type="email"
                          placeholder="<EMAIL>"
                          value={clientEmail}
                          onChange={(e) => setClientEmail(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Line Items */}
                  <div className="border-t pt-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold">Line Items</h3>
                      <Button onClick={addLineItem} size="sm">
                        <Plus className="mr-2 h-4 w-4" />
                        Add Item
                      </Button>
                    </div>
                    
                    <div className="space-y-4">
                      {lineItems.map((item, index) => (
                        <Card key={item.id} className="p-4">
                          <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
                            <div className="md:col-span-2 space-y-2">
                              <Label>Description</Label>
                              <Input
                                placeholder="Item description"
                                value={item.description}
                                onChange={(e) => updateLineItem(item.id, 'description', e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Quantity</Label>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.quantity}
                                onChange={(e) => updateLineItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Unit</Label>
                              <Select 
                                value={item.unit} 
                                onValueChange={(value) => updateLineItem(item.id, 'unit', value)}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="unit">Unit</SelectItem>
                                  <SelectItem value="hour">Hour</SelectItem>
                                  <SelectItem value="day">Day</SelectItem>
                                  <SelectItem value="sqft">Sq Ft</SelectItem>
                                  <SelectItem value="lnft">Linear Ft</SelectItem>
                                  <SelectItem value="each">Each</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Unit Price ($)</Label>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.unitPrice}
                                onChange={(e) => updateLineItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                              />
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="text-right">
                                <Label className="text-sm text-muted-foreground">Total</Label>
                                <div className="font-semibold">${item.total.toFixed(2)}</div>
                              </div>
                              {lineItems.length > 1 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeLineItem(item.id)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>

                  {/* Totals */}
                  <div className="border-t pt-6">
                    <div className="max-w-md ml-auto space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span className="font-semibold">${calculateSubtotal().toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax (10%):</span>
                        <span className="font-semibold">${calculateTax(calculateSubtotal()).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-lg font-bold border-t pt-2">
                        <span>Total:</span>
                        <span>${calculateTotal().toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="border-t pt-6">
                    <div className="flex flex-col lg:flex-row justify-between gap-4">
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Button variant="outline">
                          <Download className="mr-2 h-4 w-4" />
                          Download PDF
                        </Button>
                        <Button variant="outline">
                          Save Draft
                        </Button>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Button variant="outline">
                          <Users className="mr-2 h-4 w-4" />
                          Generate Client Link
                        </Button>
                        <Button className="bg-accent hover:bg-accent/90">
                          <Send className="mr-2 h-4 w-4" />
                          Send to Client
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2 text-center">
                      Quotations are automatically saved and can be shared via client portal or unique access links
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates" className="space-y-6">
              {/* Templates */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Quotation Templates
                  </CardTitle>
                  <CardDescription>
                    Choose from pre-built templates designed for different types of construction quotations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {quotationTemplates.map((template) => (
                      <Card key={template.id} className="border-2 border-dashed hover:border-accent transition-colors cursor-pointer group">
                        <CardHeader className="px-4 py-3">
                          <div className="flex items-center justify-between mb-2">
                            <Badge variant="outline" className="text-xs">
                              {template.category}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              Quotation
                            </Badge>
                          </div>
                          <CardTitle className="text-base flex items-center">
                            <DollarSign className="mr-2 h-4 w-4 group-hover:text-accent transition-colors" />
                            <span className="text-sm">{template.name}</span>
                          </CardTitle>
                          <CardDescription className="text-xs">{template.description}</CardDescription>
                        </CardHeader>
                        <CardContent className="pt-0 px-4 pb-4">
                          <Button className="w-full text-sm bg-accent hover:bg-accent/90 group-hover:bg-accent group-hover:text-white transition-colors">
                            <Plus className="mr-2 h-4 w-4" />
                            Use Template
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                  <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold mb-2 text-sm">Quotation Features:</h4>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      <li>• Automatic line-item calculations and totals</li>
                      <li>• Professional PDF generation with your branding</li>
                      <li>• Client approval and feedback collection</li>
                      <li>• Integration with project management workflow</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default StandaloneQuotationBuilder;
