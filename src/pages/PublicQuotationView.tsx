import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Calculator, 
  CheckCircle, 
  Calendar, 
  DollarSign, 
  MapPin, 
  User, 
  Mail, 
  Phone,
  Building,
  Clock,
  FileText,
  ThumbsUp,
  ThumbsDown,
  AlertCircle
} from "lucide-react";

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

const PublicQuotationView = () => {
  const { quotationId } = useParams();
  const [isAccepted, setIsAccepted] = useState(false);
  const [isRejected, setIsRejected] = useState(false);
  const [responseDate, setResponseDate] = useState('');

  // Mock quotation data - in real app, this would be fetched based on quotationId
  const quotationData = {
    id: quotationId,
    title: "Kitchen Renovation Quote",
    clientName: "<PERSON> Smith",
    clientEmail: "<EMAIL>",
    clientPhone: "(*************",
    projectType: "renovation",
    projectAddress: "123 Main St, Anytown, ST 12345",
    validUntil: "2024-02-15",
    lineItems: [
      { id: '1', description: 'Premium Kitchen Cabinets - Solid Wood', quantity: 1, unitPrice: 15000, total: 15000 },
      { id: '2', description: 'Quartz Countertops - Premium Grade', quantity: 1, unitPrice: 5000, total: 5000 },
      { id: '3', description: 'Professional Installation Labor', quantity: 1, unitPrice: 8000, total: 8000 },
      { id: '4', description: 'Electrical Work & Lighting', quantity: 1, unitPrice: 2500, total: 2500 },
      { id: '5', description: 'Plumbing Modifications', quantity: 1, unitPrice: 1800, total: 1800 }
    ] as LineItem[],
    subtotal: 32300,
    taxRate: 8.5,
    taxAmount: 2745.50,
    totalAmount: 35045.50,
    description: "Complete kitchen renovation with premium materials and professional installation",
    terms: "50% deposit required upon acceptance, remaining balance due upon completion. All work guaranteed for 2 years.",
    notes: "Quote includes all materials, labor, and permits. Timeline: 3-4 weeks from start date.",
    status: 'sent',
    createdAt: "2024-01-15"
  };

  const handleAccept = () => {
    setIsAccepted(true);
    setResponseDate(new Date().toISOString().split('T')[0]);
    
    // In a real app, this would send the acceptance data to the server
    alert('✅ Quotation accepted successfully!\n\nThe contractor has been notified of your acceptance. They will contact you shortly to schedule the project start date.');
  };

  const handleReject = () => {
    setIsRejected(true);
    setResponseDate(new Date().toISOString().split('T')[0]);
    
    // In a real app, this would send the rejection data to the server
    alert('❌ Quotation declined.\n\nThe contractor has been notified. Thank you for your consideration.');
  };

  const isExpired = new Date(quotationData.validUntil) < new Date();
  const daysUntilExpiry = Math.ceil((new Date(quotationData.validUntil).getTime() - new Date().getTime()) / (1000 * 3600 * 24));

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
              <Calculator className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{quotationData.title}</h1>
          <p className="text-gray-600">Professional Construction Quotation</p>
          <Badge variant="outline" className="mt-2">
            Quote ID: {quotationData.id}
          </Badge>
        </div>

        {/* Status Banner */}
        {isAccepted ? (
          <Card className="mb-6 bg-green-50 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="font-semibold text-green-900">Quotation Accepted</h3>
                  <p className="text-green-700 text-sm">Accepted on {new Date(responseDate).toLocaleDateString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : isRejected ? (
          <Card className="mb-6 bg-red-50 border-red-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <ThumbsDown className="h-6 w-6 text-red-600" />
                <div>
                  <h3 className="font-semibold text-red-900">Quotation Declined</h3>
                  <p className="text-red-700 text-sm">Declined on {new Date(responseDate).toLocaleDateString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : isExpired ? (
          <Card className="mb-6 bg-red-50 border-red-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-6 w-6 text-red-600" />
                <div>
                  <h3 className="font-semibold text-red-900">Quotation Expired</h3>
                  <p className="text-red-700 text-sm">This quotation expired on {new Date(quotationData.validUntil).toLocaleDateString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="mb-6 bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-blue-900">Awaiting Your Response</h3>
                  <p className="text-blue-700 text-sm">
                    {daysUntilExpiry > 0 
                      ? `Valid for ${daysUntilExpiry} more day${daysUntilExpiry !== 1 ? 's' : ''} (until ${new Date(quotationData.validUntil).toLocaleDateString()})`
                      : 'Expires today'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quotation Details */}
        <div className="space-y-6">
          {/* Client Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Client Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Client Name</p>
                    <p className="font-semibold">{quotationData.clientName}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-semibold">{quotationData.clientEmail}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-semibold">{quotationData.clientPhone}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Project Address</p>
                    <p className="font-semibold">{quotationData.projectAddress}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Project Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Project Type</p>
                    <p className="font-semibold capitalize">{quotationData.projectType}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Valid Until</p>
                    <p className="font-semibold">{new Date(quotationData.validUntil).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h4 className="font-semibold mb-2">Project Description</h4>
                <p className="text-gray-700">{quotationData.description}</p>
              </div>
            </CardContent>
          </Card>

          {/* Line Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Detailed Breakdown
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {quotationData.lineItems.map((item, index) => (
                  <div key={item.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-semibold">{item.description}</h4>
                      <p className="text-sm text-gray-600">Quantity: {item.quantity} × ${item.unitPrice.toLocaleString()}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-lg">${item.total.toLocaleString()}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <Separator className="my-4" />
              
              {/* Pricing Summary */}
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span className="font-semibold">${quotationData.subtotal.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax ({quotationData.taxRate}%):</span>
                  <span className="font-semibold">${quotationData.taxAmount.toLocaleString()}</span>
                </div>
                <div className="border-t pt-2 flex justify-between text-xl font-bold text-green-600">
                  <span>Total:</span>
                  <span>${quotationData.totalAmount.toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Terms & Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Terms & Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Terms & Conditions</h4>
                <p className="text-gray-700">{quotationData.terms}</p>
              </div>
              
              {quotationData.notes && (
                <div>
                  <h4 className="font-semibold mb-2">Additional Notes</h4>
                  <p className="text-gray-700">{quotationData.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          {!isAccepted && !isRejected && !isExpired && (
            <Card>
              <CardHeader>
                <CardTitle>Your Response</CardTitle>
                <CardDescription>
                  Please review the quotation details above and choose your response
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4">
                  <Button 
                    onClick={handleAccept}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                    size="lg"
                  >
                    <ThumbsUp className="h-5 w-5 mr-2" />
                    Accept Quotation
                  </Button>
                  <Button 
                    onClick={handleReject}
                    variant="outline"
                    className="flex-1 border-red-300 text-red-600 hover:bg-red-50"
                    size="lg"
                  >
                    <ThumbsDown className="h-5 w-5 mr-2" />
                    Decline Quotation
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Footer */}
        <div className="text-center mt-8 pt-8 border-t">
          <p className="text-gray-500 text-sm">
            This quotation is valid until {new Date(quotationData.validUntil).toLocaleDateString()}
          </p>
          <p className="text-gray-400 text-xs mt-2">
            Generated by ConstructionSync Quotation Builder
          </p>
        </div>
      </div>
    </div>
  );
};

export default PublicQuotationView;
