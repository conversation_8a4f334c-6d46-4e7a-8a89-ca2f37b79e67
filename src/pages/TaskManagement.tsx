import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, CheckSquare, Clock, Users, Calendar, AlertTriangle, Filter, Search } from "lucide-react";
import { useState } from "react";
import DashboardLayout from "@/components/DashboardLayout";

const TaskManagement = () => {
  const [selectedProject, setSelectedProject] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedPriority, setSelectedPriority] = useState("all");

  // Mock task data
  const tasks = [
    {
      id: "1",
      title: "Install electrical panels on 3rd floor",
      description: "Complete installation of main electrical panels and distribution boards",
      project: "Downtown Office Complex",
      assignee: "Sarah Johnson",
      status: "in-progress",
      priority: "high",
      dueDate: "Nov 20, 2025",
      createdDate: "Nov 10, 2025",
      progress: 60,
      estimatedHours: 16,
      actualHours: 10,
      dependencies: ["Foundation electrical work"],
      tags: ["electrical", "installation"]
    },
    {
      id: "2",
      title: "Frame interior walls - Living area",
      description: "Frame all interior walls for the main living area according to blueprints",
      project: "Residential Villa",
      assignee: "Mike Chen",
      status: "completed",
      priority: "medium",
      dueDate: "Nov 15, 2025",
      createdDate: "Nov 5, 2025",
      progress: 100,
      estimatedHours: 24,
      actualHours: 22,
      dependencies: [],
      tags: ["framing", "interior"]
    },
    {
      id: "3",
      title: "Safety inspection - Roofing work",
      description: "Conduct comprehensive safety inspection of roofing installation",
      project: "Downtown Office Complex",
      assignee: "John Martinez",
      status: "pending",
      priority: "high",
      dueDate: "Nov 18, 2025",
      createdDate: "Nov 12, 2025",
      progress: 0,
      estimatedHours: 4,
      actualHours: 0,
      dependencies: ["Roofing installation"],
      tags: ["safety", "inspection"]
    },
    {
      id: "4",
      title: "Install bathroom fixtures",
      description: "Install all bathroom fixtures including toilets, sinks, and shower units",
      project: "Residential Villa",
      assignee: "Lisa Rodriguez",
      status: "overdue",
      priority: "medium",
      dueDate: "Nov 12, 2025",
      createdDate: "Nov 1, 2025",
      progress: 30,
      estimatedHours: 12,
      actualHours: 8,
      dependencies: ["Plumbing rough-in"],
      tags: ["plumbing", "fixtures"]
    },
    {
      id: "5",
      title: "HVAC system installation",
      description: "Install main HVAC system and ductwork throughout the building",
      project: "Downtown Office Complex",
      assignee: "David Wilson",
      status: "in-progress",
      priority: "high",
      dueDate: "Nov 25, 2025",
      createdDate: "Nov 8, 2025",
      progress: 25,
      estimatedHours: 40,
      actualHours: 12,
      dependencies: ["Electrical rough-in"],
      tags: ["hvac", "mechanical"]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500";
      case "in-progress": return "bg-blue-500";
      case "pending": return "bg-gray-400";
      case "overdue": return "bg-red-500";
      default: return "bg-gray-400";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-700";
      case "medium": return "bg-yellow-100 text-yellow-700";
      case "low": return "bg-green-100 text-green-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const filteredTasks = tasks.filter(task => {
    const projectMatch = selectedProject === "all" || task.project === selectedProject;
    const statusMatch = selectedStatus === "all" || task.status === selectedStatus;
    const priorityMatch = selectedPriority === "all" || task.priority === selectedPriority;
    return projectMatch && statusMatch && priorityMatch;
  });

  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(t => t.status === "completed").length;
  const overdueTasks = tasks.filter(t => t.status === "overdue").length;
  const inProgressTasks = tasks.filter(t => t.status === "in-progress").length;

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Task Management & Scheduling</h1>
            <p className="text-muted-foreground">
              Organize, assign, and track tasks across all projects with real-time progress monitoring
            </p>
          </div>
          <div className="flex space-x-2 mt-4 md:mt-0">
            <Button variant="outline">
              <Search className="mr-2 h-4 w-4" />
              Search Tasks
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Task
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalTasks}</div>
              <p className="text-xs text-muted-foreground">
                Across all projects
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{completedTasks}</div>
              <p className="text-xs text-muted-foreground">
                Tasks finished
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{inProgressTasks}</div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{overdueTasks}</div>
              <p className="text-xs text-muted-foreground">
                Need attention
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="tasks" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="tasks">Task List</TabsTrigger>
            <TabsTrigger value="kanban">Kanban Board</TabsTrigger>
            <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          </TabsList>

          <TabsContent value="tasks" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Filter className="mr-2 h-5 w-5" />
                  Filter Tasks
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label>Project</Label>
                    <Select value={selectedProject} onValueChange={setSelectedProject}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select project" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Projects</SelectItem>
                        <SelectItem value="Downtown Office Complex">Downtown Office Complex</SelectItem>
                        <SelectItem value="Residential Villa">Residential Villa</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Status</Label>
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="overdue">Overdue</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Priority</Label>
                    <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Priorities</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Search</Label>
                    <Input placeholder="Search tasks..." />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Task List */}
            <Card>
              <CardHeader>
                <CardTitle>Task Overview</CardTitle>
                <CardDescription>
                  Complete list of all tasks with progress tracking and assignment details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredTasks.map((task) => (
                    <div key={task.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4">
                        <div className="flex-1 mb-2 lg:mb-0">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="font-semibold text-lg">{task.title}</h3>
                            <Badge className={getPriorityColor(task.priority)}>
                              {task.priority}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {task.description}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {task.project} • Assigned to: {task.assignee}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={`${getStatusColor(task.status)} text-white`}>
                            {task.status.replace('-', ' ')}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div>
                          <h4 className="font-medium text-sm mb-2">Timeline</h4>
                          <div className="space-y-1">
                            <div className="flex justify-between">
                              <span className="text-sm">Created</span>
                              <span className="text-sm font-medium">{task.createdDate}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm">Due Date</span>
                              <span className={`text-sm font-medium ${task.status === 'overdue' ? 'text-red-600' : ''}`}>
                                {task.dueDate}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-sm mb-2">Progress</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm">Completion</span>
                              <span className="font-medium">{task.progress}%</span>
                            </div>
                            <Progress value={task.progress} className="h-2" />
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-sm mb-2">Time Tracking</h4>
                          <div className="space-y-1">
                            <div className="flex justify-between">
                              <span className="text-sm">Estimated</span>
                              <span className="font-medium">{task.estimatedHours}h</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm">Actual</span>
                              <span className="font-medium">{task.actualHours}h</span>
                            </div>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-sm mb-2">Actions</h4>
                          <div className="flex flex-col space-y-2">
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                            <Button variant="ghost" size="sm">
                              Edit Task
                            </Button>
                          </div>
                        </div>
                      </div>
                      
                      {task.tags.length > 0 && (
                        <div className="mt-4 pt-4 border-t">
                          <div className="flex flex-wrap gap-1">
                            {task.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {task.dependencies.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs text-muted-foreground">
                            Dependencies: {task.dependencies.join(", ")}
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="kanban" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Kanban Board</CardTitle>
                <CardDescription>
                  Drag and drop tasks between different status columns
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {["pending", "in-progress", "completed", "overdue"].map((status) => (
                    <div key={status} className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold capitalize">{status.replace('-', ' ')}</h3>
                        <Badge variant="outline">
                          {tasks.filter(t => t.status === status).length}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        {tasks.filter(t => t.status === status).map((task) => (
                          <Card key={task.id} className="p-3 cursor-pointer hover:shadow-md transition-shadow">
                            <div className="space-y-2">
                              <h4 className="font-medium text-sm">{task.title}</h4>
                              <p className="text-xs text-muted-foreground">{task.assignee}</p>
                              <div className="flex justify-between items-center">
                                <Badge className={getPriorityColor(task.priority)} variant="outline">
                                  {task.priority}
                                </Badge>
                                <span className="text-xs text-muted-foreground">{task.dueDate}</span>
                              </div>
                              <Progress value={task.progress} className="h-1" />
                            </div>
                          </Card>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="calendar" className="space-y-6">
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardHeader>
                <CardTitle>Calendar View</CardTitle>
                <CardDescription>
                  View tasks scheduled across time with deadline tracking
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Calendar className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Calendar Integration</h3>
                  <p className="text-muted-foreground mb-4">
                    Calendar view will display all tasks with their due dates and dependencies
                  </p>
                  <Button variant="outline">
                    Configure Calendar View
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default TaskManagement;
