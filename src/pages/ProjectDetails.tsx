
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Calendar,
  Users,
  FileText,
  Settings,
  Plus,
  Search,
  Clock,
  Share2,
  Copy,
  ExternalLink,
  Eye,
  CheckCircle
} from "lucide-react";
import { useParams, Link } from "react-router-dom";
import DashboardLayout from "@/components/DashboardLayout";

const ProjectDetails = () => {
  const { id } = useParams();
  const [clientPortalLink, setClientPortalLink] = useState("");
  const [showClientPortal, setShowClientPortal] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);
  
  // Mock project data - in real app would fetch based on ID
  const project = {
    id: id,
    name: "Downtown Office Complex",
    client: "Sterling Corp",
    contractor: "BuildRight Construction",
    progress: 75,
    budget: 250000,
    spent: 185000,
    status: "active",
    startDate: "Sep 1, 2025",
    dueDate: "Dec 15, 2025",
    description: "A modern 5-story office complex with retail space on ground floor, featuring sustainable design elements and advanced building systems.",
    clientPortalEnabled: true,
    clientPortalToken: `${id}-client-${Math.random().toString(36).substr(2, 9)}`,
    phases: [
      { name: "Foundation", progress: 100, status: "completed", budget: 45000, spent: 43500, dueDate: "Sep 15, 2025" },
      { name: "Structural Framing", progress: 100, status: "completed", budget: 65000, spent: 67200, dueDate: "Oct 10, 2025" },
      { name: "Roofing", progress: 100, status: "completed", budget: 35000, spent: 34800, dueDate: "Nov 1, 2025" },
      { name: "MEP Installation", progress: 60, status: "active", budget: 55000, spent: 25000, dueDate: "Nov 25, 2025" },
      { name: "Interior Finishes", progress: 20, status: "active", budget: 40000, spent: 12000, dueDate: "Dec 10, 2025" },
      { name: "Final Inspection", progress: 0, status: "pending", budget: 10000, spent: 0, dueDate: "Dec 15, 2025" }
    ],
    team: [
      { name: "Mike Johnson", role: "Project Manager", email: "<EMAIL>" },
      { name: "Sarah Chen", role: "Site Supervisor", email: "<EMAIL>" },
      { name: "David Rodriguez", role: "Electrical Contractor", email: "<EMAIL>" },
      { name: "Lisa Park", role: "MEP Engineer", email: "<EMAIL>" }
    ],
    materials: [
      { name: "Steel Reinforcement Bars", quantity: 150, cost: 18750, status: "delivered" },
      { name: "Concrete Mix", quantity: 25, cost: 3750, status: "low-stock" },
      { name: "Electrical Components", quantity: 1, cost: 15000, status: "ordered" }
    ],
    documents: [
      { name: "Building Permits", type: "permit", uploadDate: "Aug 25, 2025", status: "approved" },
      { name: "Architectural Plans v2.1", type: "blueprint", uploadDate: "Nov 10, 2025", status: "current" },
      { name: "MEP Drawings", type: "blueprint", uploadDate: "Nov 5, 2025", status: "current" },
      { name: "Safety Inspection Report", type: "report", uploadDate: "Nov 12, 2025", status: "approved" }
    ]
  };

  const completedPhases = project.phases.filter(p => p.status === "completed").length;
  const remainingBudget = project.budget - project.spent;
  const budgetPercentage = (project.spent / project.budget) * 100;

  // Client Portal Functions
  const generateClientPortalLink = () => {
    const baseUrl = window.location.origin;
    const link = `${baseUrl}/client/${project.clientPortalToken}`;
    setClientPortalLink(link);
    setShowClientPortal(true);
  };

  const copyLinkToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(clientPortalLink);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const openClientPortal = () => {
    if (clientPortalLink) {
      window.open(clientPortalLink, '_blank');
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Project Header */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Link to="/dashboard" className="text-muted-foreground hover:text-primary">
                  Dashboard
                </Link>
                <span className="text-muted-foreground">/</span>
                <span>Project Details</span>
              </div>
              <h1 className="text-3xl font-bold mb-2">{project.name}</h1>
              <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                <span>Client: {project.client}</span>
                <span>•</span>
                <span>Contractor: {project.contractor}</span>
                <span>•</span>
                <span>Started: {project.startDate}</span>
                <span>•</span>
                <span>Due: {project.dueDate}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2 mt-4 md:mt-0">
              <Button
                variant="outline"
                onClick={generateClientPortalLink}
                className="flex items-center"
              >
                <Share2 className="mr-2 h-4 w-4" />
                Generate Client Link
              </Button>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Update Progress
              </Button>
            </div>
          </div>
          
          <p className="text-muted-foreground max-w-3xl">{project.description}</p>
        </div>

        {/* Client Portal Link Section */}
        {showClientPortal && (
          <Card className="border-2 border-blue-500/20 bg-blue-50/50">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-700">
                <Share2 className="mr-2 h-5 w-5" />
                Client Portal Link Generated
              </CardTitle>
              <CardDescription>
                Share this secure link with your client to give them real-time access to project updates,
                progress tracking, and the ability to leave comments and feedback.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="clientLink">Shareable Client Portal Link</Label>
                <div className="flex space-x-2">
                  <Input
                    id="clientLink"
                    value={clientPortalLink}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyLinkToClipboard}
                    className="flex items-center"
                  >
                    {linkCopied ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="mr-2 h-4 w-4" />
                        Copy
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={openClientPortal}
                    className="flex items-center"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                </div>
              </div>

              <Alert>
                <Eye className="h-4 w-4" />
                <AlertDescription>
                  <strong>Client Access Features:</strong>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>• Real-time project progress and milestone tracking</li>
                    <li>• Budget and timeline visibility</li>
                    <li>• Document and photo gallery access</li>
                    <li>• Comment and feedback system</li>
                    <li>• Activity feed with latest updates</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="flex items-center justify-between pt-2 border-t">
                <div className="text-sm text-muted-foreground">
                  <span className="font-medium">Access Token:</span> {project.clientPortalToken}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowClientPortal(false)}
                >
                  Dismiss
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-2 border-primary/20">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-lg">Progress</CardTitle>
              <div className="text-3xl font-bold text-primary">{project.progress}%</div>
            </CardHeader>
            <CardContent>
              <Progress value={project.progress} className="mb-2" />
              <p className="text-xs text-center text-muted-foreground">
                {completedPhases} of {project.phases.length} phases complete
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-green-500/20">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-lg">Budget</CardTitle>
              <div className="text-3xl font-bold text-green-600">
                ${(project.budget / 1000).toFixed(0)}K
              </div>
            </CardHeader>
            <CardContent>
              <Progress value={budgetPercentage} className="mb-2" />
              <p className="text-xs text-center text-muted-foreground">
                ${(remainingBudget / 1000).toFixed(0)}K remaining
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-blue-500/20">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-lg">Timeline</CardTitle>
              <div className="text-3xl font-bold text-blue-600">45</div>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-center text-muted-foreground mb-2">Days remaining</p>
              <Badge variant="default" className="w-full justify-center bg-green-500">
                On Schedule
              </Badge>
            </CardContent>
          </Card>

          <Card className="border-2 border-orange-500/20">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-lg">Team</CardTitle>
              <div className="text-3xl font-bold text-orange-600">{project.team.length}</div>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-center text-muted-foreground">
                Active team members
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Project Details Tabs */}
        <Tabs defaultValue="phases" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="phases">Phases</TabsTrigger>
            <TabsTrigger value="materials">Materials</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="phases" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Phases</CardTitle>
                <CardDescription>
                  Track progress and manage each phase of the construction project
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {project.phases.map((phase, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 rounded-full ${
                            phase.status === "completed" ? "bg-green-500" :
                            phase.status === "active" ? "bg-blue-500" : "bg-gray-300"
                          }`}></div>
                          <div>
                            <h3 className="font-semibold text-lg">{phase.name}</h3>
                            <p className="text-sm text-muted-foreground">Due: {phase.dueDate}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 mt-2 md:mt-0">
                          <Badge variant={phase.status === "completed" ? "default" : "outline"}>
                            {phase.status}
                          </Badge>
                          <span className="text-sm font-medium">{phase.progress}%</span>
                        </div>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <Progress value={phase.progress} className="mb-2" />
                          <div className="flex justify-between text-sm text-muted-foreground">
                            <span>Progress</span>
                            <span>{phase.progress}% Complete</span>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Budget</span>
                            <span className="font-medium">${(phase.budget / 1000).toFixed(0)}K</span>
                          </div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Spent</span>
                            <span className={`font-medium ${
                              phase.spent > phase.budget ? "text-red-600" : "text-green-600"
                            }`}>
                              ${(phase.spent / 1000).toFixed(0)}K
                            </span>
                          </div>
                          <Progress 
                            value={Math.min((phase.spent / phase.budget) * 100, 100)} 
                            className="h-2"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="materials">
            <Card>
              <CardHeader>
                <CardTitle>Material Inventory</CardTitle>
                <CardDescription>
                  Materials allocated to this project
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.materials.map((material, index) => (
                    <div key={index} className="flex justify-between items-center p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium">{material.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          Quantity: {material.quantity} • Cost: ${material.cost.toLocaleString()}
                        </p>
                      </div>
                      <Badge variant={
                        material.status === "delivered" ? "default" :
                        material.status === "low-stock" ? "destructive" : "secondary"
                      }>
                        {material.status.replace('-', ' ')}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="team">
            <Card>
              <CardHeader>
                <CardTitle>Project Team</CardTitle>
                <CardDescription>
                  Team members assigned to this project
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {project.team.map((member, index) => (
                    <div key={index} className="flex items-center space-x-3 p-4 border rounded-lg">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium">{member.name}</h3>
                        <p className="text-sm text-muted-foreground">{member.role}</p>
                        <p className="text-xs text-muted-foreground">{member.email}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              <CardHeader>
                <CardTitle>Project Documents</CardTitle>
                <CardDescription>
                  Blueprints, permits, reports, and other project documentation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.documents.map((doc, index) => (
                    <div key={index} className="flex justify-between items-center p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <FileText className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <h3 className="font-medium">{doc.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {doc.type} • Uploaded {doc.uploadDate}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={doc.status === "approved" ? "default" : "secondary"}>
                          {doc.status}
                        </Badge>
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest project updates and activity timeline
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-l-2 border-blue-200 pl-4">
                    <div className="flex justify-between items-center mb-1">
                      <Badge variant="outline">progress</Badge>
                      <span className="text-xs text-muted-foreground">2 hours ago</span>
                    </div>
                    <p className="text-sm">Electrical wiring completed for 3rd floor</p>
                  </div>
                  <div className="border-l-2 border-green-200 pl-4">
                    <div className="flex justify-between items-center mb-1">
                      <Badge variant="outline">materials</Badge>
                      <span className="text-xs text-muted-foreground">1 day ago</span>
                    </div>
                    <p className="text-sm">New materials delivered: 200 steel reinforcement bars</p>
                  </div>
                  <div className="border-l-2 border-purple-200 pl-4">
                    <div className="flex justify-between items-center mb-1">
                      <Badge variant="outline">milestone</Badge>
                      <span className="text-xs text-muted-foreground">3 days ago</span>
                    </div>
                    <p className="text-sm">Roofing phase completed and approved</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ProjectDetails;
