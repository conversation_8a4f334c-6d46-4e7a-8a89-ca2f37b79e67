import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Calculator, 
  Plus, 
  Search, 
  Filter, 
  Settings, 
  LogOut, 
  Edit, 
  Send, 
  Copy, 
  Trash2,
  Eye,
  Calendar,
  DollarSign,
  User,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  FileText
} from "lucide-react";
import { useQuotationAuth } from "@/contexts/QuotationAuthContext";

const QuotationDashboard = () => {
  const { user, quotations, logout, resendQuotation, duplicateQuotation, deleteQuotation } = useQuotationAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'sent': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'draft': return <Edit className="h-4 w-4 text-gray-500" />;
      case 'expired': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'rejected': return <AlertCircle className="h-4 w-4 text-orange-500" />;
      default: return <Calculator className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200';
      case 'sent': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'draft': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'expired': return 'bg-red-100 text-red-800 border-red-200';
      case 'rejected': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const filteredQuotations = quotations.filter(quotation => {
    const matchesSearch = quotation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quotation.clientName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || quotation.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const quotationStats = {
    total: quotations.length,
    approved: quotations.filter(q => q.status === 'approved').length,
    pending: quotations.filter(q => q.status === 'sent').length,
    draft: quotations.filter(q => q.status === 'draft').length
  };

  const totalValue = quotations
    .filter(q => q.status === 'approved')
    .reduce((sum, quotation) => sum + quotation.total, 0);

  const handleResend = async (quotationId: string) => {
    await resendQuotation(quotationId);
  };

  const handleDuplicate = (quotationId: string) => {
    duplicateQuotation(quotationId);
  };

  const handleDelete = (quotationId: string) => {
    if (window.confirm('Are you sure you want to delete this quotation?')) {
      deleteQuotation(quotationId);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-accent rounded-lg flex items-center justify-center">
                <Calculator className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-accent">Quotation Builder</h1>
                <p className="text-sm text-muted-foreground">Welcome back, {user?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/contracts" className="text-muted-foreground hover:text-primary transition-colors">
                Contract Builder
              </Link>
              <Button variant="outline" onClick={logout} className="flex items-center space-x-2">
                <LogOut className="h-4 w-4" />
                <span>Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Quotations</p>
                  <p className="text-2xl font-bold">{quotationStats.total}</p>
                </div>
                <Calculator className="h-8 w-8 text-accent" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Approved Quotes</p>
                  <p className="text-2xl font-bold text-green-600">{quotationStats.approved}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Pending Response</p>
                  <p className="text-2xl font-bold text-blue-600">{quotationStats.pending}</p>
                </div>
                <Clock className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                  <p className="text-2xl font-bold text-accent">${totalValue.toLocaleString()}</p>
                </div>
                <DollarSign className="h-8 w-8 text-accent" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Actions and Filters */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search quotations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="sent">Sent</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="expired">Expired</option>
            </select>
          </div>
          <Button asChild className="bg-accent hover:bg-accent/90">
            <Link to="/quotations" className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>Create New Quotation</span>
            </Link>
          </Button>
        </div>

        {/* Quotations List */}
        <div className="space-y-4">
          {filteredQuotations.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Calculator className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No quotations found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || statusFilter !== "all" 
                    ? "Try adjusting your search or filter criteria"
                    : "Get started by creating your first quotation"
                  }
                </p>
                <Button asChild>
                  <Link to="/quotations">Create Your First Quotation</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredQuotations.map((quotation) => (
              <Card key={quotation.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold">{quotation.title}</h3>
                        <Badge className={`${getStatusColor(quotation.status)} flex items-center space-x-1`}>
                          {getStatusIcon(quotation.status)}
                          <span className="capitalize">{quotation.status}</span>
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground mb-4">
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4" />
                          <span>{quotation.clientName}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4" />
                          <span>${quotation.total.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>Created {quotation.createdDate}</span>
                        </div>
                      </div>

                      <div className="text-sm text-muted-foreground">
                        <span className="font-medium">{quotation.lineItems.length} line items</span>
                        {quotation.validUntil && (
                          <span className="ml-4">Valid until {quotation.validUntil}</span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <Button variant="outline" size="sm" title="View Quotation">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Edit Quotation" asChild>
                        <Link to={`/quotations?edit=${quotation.id}`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                      {quotation.status === 'draft' || quotation.status === 'expired' ? (
                        <Button 
                          variant="outline" 
                          size="sm"
                          title="Resend Quotation"
                          onClick={() => handleResend(quotation.id)}
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      ) : null}
                      <Button 
                        variant="outline" 
                        size="sm"
                        title="Duplicate Quotation"
                        onClick={() => handleDuplicate(quotation.id)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      {quotation.status === 'approved' && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          title="Convert to Contract"
                          asChild
                        >
                          <Link to={`/contracts?from_quote=${quotation.id}`}>
                            <FileText className="h-4 w-4" />
                          </Link>
                        </Button>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        title="Delete Quotation"
                        onClick={() => handleDelete(quotation.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default QuotationDashboard;
