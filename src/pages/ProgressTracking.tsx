
import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Plus, Calendar, Users, FileText, Clock } from "lucide-react";
import DashboardLayout from "@/components/DashboardLayout";

const ProgressTracking = () => {
  const projects = [
    {
      id: "1",
      name: "Downtown Office Complex",
      client: "Sterling Corp",
      overallProgress: 75,
      phases: [
        { name: "Foundation", progress: 100, status: "completed", dueDate: "Sep 15, 2025" },
        { name: "Structural Framing", progress: 100, status: "completed", dueDate: "Oct 10, 2025" },
        { name: "Roofing", progress: 100, status: "completed", dueDate: "Nov 1, 2025" },
        { name: "MEP Installation", progress: 60, status: "active", dueDate: "Nov 25, 2025" },
        { name: "Interior Finishes", progress: 20, status: "active", dueDate: "Dec 10, 2025" },
        { name: "Final Inspection", progress: 0, status: "pending", dueDate: "Dec 15, 2025" }
      ],
      recentUpdates: [
        { date: "2 hours ago", update: "Electrical wiring completed for 3rd floor", phase: "MEP Installation" },
        { date: "1 day ago", update: "HVAC system installation started", phase: "MEP Installation" },
        { date: "3 days ago", update: "Roofing inspection passed", phase: "Roofing" }
      ]
    },
    {
      id: "2",
      name: "Residential Villa",
      client: "Johnson Family", 
      overallProgress: 45,
      phases: [
        { name: "Foundation", progress: 100, status: "completed", dueDate: "Oct 1, 2025" },
        { name: "Framing", progress: 85, status: "active", dueDate: "Nov 15, 2025" },
        { name: "Roofing", progress: 30, status: "active", dueDate: "Dec 1, 2025" },
        { name: "Utilities", progress: 0, status: "pending", dueDate: "Dec 15, 2025" },
        { name: "Interior", progress: 0, status: "pending", dueDate: "Jan 20, 2026" },
        { name: "Landscaping", progress: 0, status: "pending", dueDate: "Jan 30, 2026" }
      ],
      recentUpdates: [
        { date: "4 hours ago", update: "Wall framing 85% complete", phase: "Framing" },
        { date: "2 days ago", update: "Roof trusses delivered", phase: "Roofing" },
        { date: "1 week ago", update: "Foundation curing completed", phase: "Foundation" }
      ]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500";
      case "active": return "bg-blue-500";
      case "pending": return "bg-gray-400";
      case "delayed": return "bg-red-500";
      default: return "bg-gray-400";
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return "bg-green-500";
    if (progress >= 50) return "bg-blue-500";
    if (progress > 0) return "bg-yellow-500";
    return "bg-gray-300";
  };

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Progress Tracking & Milestones</h1>
            <p className="text-muted-foreground">
              Monitor project phases, track milestones, and document progress with real-time updates
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Update Progress
          </Button>
        </div>

        {/* Progress Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Progress</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(projects.reduce((sum, p) => sum + p.overallProgress, 0) / projects.length)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Across all projects
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Phases</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {projects.reduce((sum, p) => sum + p.phases.filter(phase => phase.status === "completed").length, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Milestones achieved
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Phases</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {projects.reduce((sum, p) => sum + p.phases.filter(phase => phase.status === "active").length, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently in progress
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Recent Updates</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {projects.reduce((sum, p) => sum + p.recentUpdates.length, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                In the last week
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Project Progress Details */}
        <div className="space-y-8">
          {projects.map((project) => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                  <div>
                    <CardTitle className="text-xl">{project.name}</CardTitle>
                    <CardDescription>Client: {project.client}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-4 mt-4 md:mt-0">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{project.overallProgress}%</div>
                      <p className="text-xs text-muted-foreground">Complete</p>
                    </div>
                    <Button variant="outline" size="sm">
                      Share Progress
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="grid lg:grid-cols-2 gap-8">
                  {/* Project Phases */}
                  <div>
                    <h3 className="font-semibold mb-4">Project Phases</h3>
                    <div className="space-y-4">
                      {project.phases.map((phase, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center space-x-2">
                              <div className={`w-3 h-3 rounded-full ${getStatusColor(phase.status)}`}></div>
                              <span className="font-medium">{phase.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {phase.status}
                              </Badge>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium">{phase.progress}%</span>
                              <span className="text-xs text-muted-foreground">Due: {phase.dueDate}</span>
                            </div>
                          </div>
                          <Progress 
                            value={phase.progress} 
                            className="h-2"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Recent Updates */}
                  <div>
                    <h3 className="font-semibold mb-4">Recent Progress Updates</h3>
                    <div className="space-y-4">
                      {project.recentUpdates.map((update, index) => (
                        <div key={index} className="border-l-2 border-primary/20 pl-4">
                          <div className="flex justify-between items-start mb-1">
                            <Badge variant="outline" className="text-xs">
                              {update.phase}
                            </Badge>
                            <span className="text-xs text-muted-foreground">{update.date}</span>
                          </div>
                          <p className="text-sm">{update.update}</p>
                        </div>
                      ))}
                    </div>
                    
                    <Button variant="outline" size="sm" className="w-full mt-4">
                      View All Updates
                    </Button>
                  </div>
                </div>
                
                {/* Timeline Overview */}
                <div className="mt-8 pt-6 border-t">
                  <h3 className="font-semibold mb-4">Project Timeline</h3>
                  <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                    <div className="text-center">
                      <p className="text-sm font-medium">Project Started</p>
                      <p className="text-xs text-muted-foreground">Sep 1, 2025</p>
                    </div>
                    <div className="flex-1 mx-4">
                      <Progress value={project.overallProgress} className="h-3" />
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium">Expected Completion</p>
                      <p className="text-xs text-muted-foreground">
                        {project.id === "1" ? "Dec 15, 2025" : "Jan 30, 2026"}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Progress Analytics */}
        <Card className="mt-8 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
          <CardHeader>
            <CardTitle>Progress Analytics & Forecasting</CardTitle>
            <CardDescription>
              Data-driven insights into project velocity and completion predictions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">92%</div>
                <p className="text-sm text-muted-foreground">On-time completion rate</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 mb-1">5.2%</div>
                <p className="text-sm text-muted-foreground">Average progress weekly</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 mb-1">2 weeks</div>
                <p className="text-sm text-muted-foreground">Ahead of schedule</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ProgressTracking;
