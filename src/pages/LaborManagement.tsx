import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Users, Clock, Calendar, DollarSign, Search, Filter } from "lucide-react";
import { useState } from "react";
import Navigation from "@/components/Navigation";

const LaborManagement = () => {
  const [selectedProject, setSelectedProject] = useState("all");
  const [selectedRole, setSelectedRole] = useState("all");

  // Mock labor data
  const laborData = [
    {
      id: "1",
      name: "<PERSON>",
      role: "Site Supervisor",
      project: "Downtown Office Complex",
      hourlyRate: 45,
      hoursWorked: 160,
      totalEarnings: 7200,
      status: "active",
      skills: ["Project Management", "Safety Compliance", "Team Leadership"],
      startDate: "Sep 1, 2025",
      phone: "(*************",
      email: "<EMAIL>"
    },
    {
      id: "2", 
      name: "Sarah Johnson",
      role: "Electrician",
      project: "Downtown Office Complex",
      hourlyRate: 38,
      hoursWorked: 120,
      totalEarnings: 4560,
      status: "active",
      skills: ["Electrical Installation", "Wiring", "Safety Protocols"],
      startDate: "Sep 15, 2025",
      phone: "(*************",
      email: "<EMAIL>"
    },
    {
      id: "3",
      name: "Mike Chen",
      role: "Carpenter",
      project: "Residential Villa",
      hourlyRate: 32,
      hoursWorked: 140,
      totalEarnings: 4480,
      status: "active",
      skills: ["Framing", "Finish Carpentry", "Blueprint Reading"],
      startDate: "Oct 1, 2025",
      phone: "(*************",
      email: "<EMAIL>"
    },
    {
      id: "4",
      name: "Lisa Rodriguez",
      role: "Plumber",
      project: "Downtown Office Complex",
      hourlyRate: 40,
      hoursWorked: 80,
      totalEarnings: 3200,
      status: "on-leave",
      skills: ["Pipe Installation", "Fixture Setup", "Leak Repair"],
      startDate: "Sep 20, 2025",
      phone: "(*************",
      email: "<EMAIL>"
    }
  ];

  const timeEntries = [
    {
      id: "1",
      workerId: "1",
      workerName: "John Martinez",
      project: "Downtown Office Complex",
      date: "Nov 15, 2025",
      hoursWorked: 8,
      overtime: 2,
      task: "Site supervision and safety inspection",
      status: "approved"
    },
    {
      id: "2",
      workerId: "2", 
      workerName: "Sarah Johnson",
      project: "Downtown Office Complex",
      date: "Nov 15, 2025",
      hoursWorked: 8,
      overtime: 0,
      task: "Electrical panel installation",
      status: "pending"
    },
    {
      id: "3",
      workerId: "3",
      workerName: "Mike Chen", 
      project: "Residential Villa",
      date: "Nov 15, 2025",
      hoursWorked: 7,
      overtime: 0,
      task: "Interior framing work",
      status: "approved"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500";
      case "on-leave": return "bg-yellow-500";
      case "inactive": return "bg-gray-500";
      default: return "bg-gray-500";
    }
  };

  const getTimeStatusColor = (status: string) => {
    switch (status) {
      case "approved": return "bg-green-500";
      case "pending": return "bg-yellow-500";
      case "rejected": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };

  const filteredWorkers = laborData.filter(worker => {
    const projectMatch = selectedProject === "all" || worker.project === selectedProject;
    const roleMatch = selectedRole === "all" || worker.role === selectedRole;
    return projectMatch && roleMatch;
  });

  const totalLaborCost = laborData.reduce((sum, worker) => sum + worker.totalEarnings, 0);
  const activeWorkers = laborData.filter(w => w.status === "active").length;
  const totalHours = laborData.reduce((sum, worker) => sum + worker.hoursWorked, 0);
  const avgHourlyRate = laborData.reduce((sum, worker) => sum + worker.hourlyRate, 0) / laborData.length;

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Labor Management & Workforce</h1>
            <p className="text-muted-foreground">
              Manage workforce, track time entries, and monitor labor costs across all projects
            </p>
          </div>
          <div className="flex space-x-2 mt-4 md:mt-0">
            <Button variant="outline">
              <Search className="mr-2 h-4 w-4" />
              Find Workers
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Worker
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Labor Cost</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalLaborCost / 1000).toFixed(1)}K</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Workers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{activeWorkers}</div>
              <p className="text-xs text-muted-foreground">
                Currently employed
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{totalHours}</div>
              <p className="text-xs text-muted-foreground">
                Hours logged
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Hourly Rate</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${avgHourlyRate.toFixed(0)}</div>
              <p className="text-xs text-muted-foreground">
                Per hour average
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="workforce" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="workforce">Workforce</TabsTrigger>
            <TabsTrigger value="timesheet">Time Tracking</TabsTrigger>
            <TabsTrigger value="payroll">Payroll</TabsTrigger>
          </TabsList>

          <TabsContent value="workforce" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Filter className="mr-2 h-5 w-5" />
                  Filter Workforce
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Project</Label>
                    <Select value={selectedProject} onValueChange={setSelectedProject}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select project" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Projects</SelectItem>
                        <SelectItem value="Downtown Office Complex">Downtown Office Complex</SelectItem>
                        <SelectItem value="Residential Villa">Residential Villa</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Role</Label>
                    <Select value={selectedRole} onValueChange={setSelectedRole}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Roles</SelectItem>
                        <SelectItem value="Site Supervisor">Site Supervisor</SelectItem>
                        <SelectItem value="Electrician">Electrician</SelectItem>
                        <SelectItem value="Carpenter">Carpenter</SelectItem>
                        <SelectItem value="Plumber">Plumber</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Search</Label>
                    <Input placeholder="Search by name..." />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Worker List */}
            <Card>
              <CardHeader>
                <CardTitle>Workforce Directory</CardTitle>
                <CardDescription>
                  Complete overview of all workers with their roles, rates, and performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredWorkers.map((worker) => (
                    <div key={worker.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4">
                        <div className="flex items-center space-x-3 mb-2 lg:mb-0">
                          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                            <Users className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">{worker.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {worker.role} • {worker.project}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={`${getStatusColor(worker.status)} text-white`}>
                            {worker.status.replace('-', ' ')}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div>
                          <h4 className="font-medium text-sm mb-2">Contact Information</h4>
                          <div className="space-y-1">
                            <p className="text-sm">{worker.phone}</p>
                            <p className="text-sm text-muted-foreground">{worker.email}</p>
                            <p className="text-xs text-muted-foreground">Started: {worker.startDate}</p>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-sm mb-2">Work Statistics</h4>
                          <div className="space-y-1">
                            <div className="flex justify-between">
                              <span className="text-sm">Hours Worked</span>
                              <span className="font-medium">{worker.hoursWorked}h</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm">Hourly Rate</span>
                              <span className="font-medium">${worker.hourlyRate}/hr</span>
                            </div>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-sm mb-2">Skills & Expertise</h4>
                          <div className="flex flex-wrap gap-1">
                            {worker.skills.slice(0, 2).map((skill, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {skill}
                              </Badge>
                            ))}
                            {worker.skills.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{worker.skills.length - 2} more
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-sm mb-2">Total Earnings</h4>
                          <div className="text-xl font-bold text-primary mb-2">
                            ${worker.totalEarnings.toLocaleString()}
                          </div>
                          <div className="flex flex-col space-y-1">
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                            <Button variant="ghost" size="sm">
                              Edit Profile
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="timesheet" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Time Entry Management</CardTitle>
                <CardDescription>
                  Review and approve worker time entries and overtime
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {timeEntries.map((entry) => (
                    <div key={entry.id} className="border rounded-lg p-4">
                      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="font-semibold">{entry.workerName}</h3>
                            <Badge className={`${getTimeStatusColor(entry.status)} text-white text-xs`}>
                              {entry.status}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">
                            {entry.project} • {entry.date}
                          </p>
                          <p className="text-sm">{entry.task}</p>
                        </div>
                        <div className="flex items-center space-x-4 mt-4 md:mt-0">
                          <div className="text-center">
                            <div className="text-lg font-bold">{entry.hoursWorked}h</div>
                            <p className="text-xs text-muted-foreground">Regular</p>
                          </div>
                          {entry.overtime > 0 && (
                            <div className="text-center">
                              <div className="text-lg font-bold text-orange-600">{entry.overtime}h</div>
                              <p className="text-xs text-muted-foreground">Overtime</p>
                            </div>
                          )}
                          <div className="flex space-x-2">
                            {entry.status === "pending" && (
                              <>
                                <Button variant="outline" size="sm">Reject</Button>
                                <Button size="sm">Approve</Button>
                              </>
                            )}
                            {entry.status === "approved" && (
                              <Button variant="outline" size="sm">View Details</Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payroll" className="space-y-6">
            <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
              <CardHeader>
                <CardTitle>Payroll Summary</CardTitle>
                <CardDescription>
                  Current period payroll calculations and processing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">
                      ${totalLaborCost.toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Payroll</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">{activeWorkers}</div>
                    <p className="text-sm text-muted-foreground">Workers to Pay</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600 mb-1">
                      {timeEntries.filter(e => e.status === "pending").length}
                    </div>
                    <p className="text-sm text-muted-foreground">Pending Approvals</p>
                  </div>
                </div>
                <div className="flex justify-center mt-6">
                  <Button className="w-full md:w-auto">
                    Process Payroll
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default LaborManagement;
