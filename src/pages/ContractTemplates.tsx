import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  FileText, 
  Search, 
  Filter,
  Eye,
  Download,
  Star,
  Building,
  Home,
  Wrench,
  Palette,
  Shield,
  Zap
} from "lucide-react";
import ContractDashboardLayout from "@/components/ContractDashboardLayout";
import TemplatePreviewModal from "@/components/TemplatePreviewModal";

interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  category: "classic" | "modern" | "creative" | "legal";
  type: "residential" | "commercial" | "renovation" | "maintenance" | "design";
  rating: number;
  downloads: number;
  preview: string;
  features: string[];
  color: string;
  icon: any;
}

const ContractTemplates = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [previewTemplate, setPreviewTemplate] = useState<ContractTemplate | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const templates: ContractTemplate[] = [
    // Classic Professional Templates
    {
      id: "classic-residential-1",
      name: "Classic Residential Construction",
      description: "Traditional, comprehensive residential construction contract with detailed terms and conditions",
      category: "classic",
      type: "residential",
      rating: 4.9,
      downloads: 1247,
      preview: "A formal, traditional contract layout with serif fonts and structured sections",
      features: ["Detailed payment schedules", "Comprehensive legal clauses", "Material specifications", "Timeline management"],
      color: "bg-slate-50 border-slate-200",
      icon: Home
    },
    {
      id: "classic-commercial-1",
      name: "Classic Commercial Development",
      description: "Professional commercial construction contract with extensive legal protections",
      category: "classic",
      type: "commercial",
      rating: 4.8,
      downloads: 892,
      preview: "Formal business contract with traditional formatting and comprehensive coverage",
      features: ["Multi-phase project management", "Subcontractor agreements", "Insurance requirements", "Quality standards"],
      color: "bg-slate-50 border-slate-200",
      icon: Building
    },
    {
      id: "classic-renovation-1",
      name: "Classic Home Renovation",
      description: "Time-tested renovation contract template with proven legal framework",
      category: "classic",
      type: "renovation",
      rating: 4.7,
      downloads: 634,
      preview: "Traditional renovation contract with detailed scope and change order provisions",
      features: ["Change order management", "Permit handling", "Existing structure considerations", "Cleanup provisions"],
      color: "bg-slate-50 border-slate-200",
      icon: Wrench
    },

    // Modern Minimalist Templates
    {
      id: "modern-residential-1",
      name: "Modern Residential Build",
      description: "Clean, contemporary contract design with streamlined terms and visual clarity",
      category: "modern",
      type: "residential",
      rating: 4.9,
      downloads: 1456,
      preview: "Sleek, modern layout with clean typography and organized sections",
      features: ["Digital signatures", "Progress tracking", "Photo documentation", "Real-time updates"],
      color: "bg-blue-50 border-blue-200",
      icon: Home
    },
    {
      id: "modern-commercial-1",
      name: "Modern Commercial Complex",
      description: "Contemporary commercial contract with innovative project management features",
      category: "modern",
      type: "commercial",
      rating: 4.8,
      downloads: 1123,
      preview: "Modern business contract with clean design and efficient organization",
      features: ["Agile project phases", "Digital collaboration", "Performance metrics", "Sustainability clauses"],
      color: "bg-blue-50 border-blue-200",
      icon: Building
    },
    {
      id: "modern-design-1",
      name: "Modern Design-Build",
      description: "Integrated design-build contract with contemporary project delivery methods",
      category: "modern",
      type: "design",
      rating: 4.9,
      downloads: 789,
      preview: "Streamlined design-build contract with modern project delivery approach",
      features: ["Integrated design process", "Value engineering", "BIM integration", "Collaborative delivery"],
      color: "bg-blue-50 border-blue-200",
      icon: Palette
    },

    // Creative Contemporary Templates
    {
      id: "creative-residential-1",
      name: "Creative Custom Home",
      description: "Innovative contract design for unique residential projects with artistic elements",
      category: "creative",
      type: "residential",
      rating: 4.7,
      downloads: 567,
      preview: "Creative layout with visual elements and modern typography for unique projects",
      features: ["Custom design elements", "Artistic specifications", "Unique materials", "Creative timelines"],
      color: "bg-purple-50 border-purple-200",
      icon: Home
    },
    {
      id: "creative-renovation-1",
      name: "Creative Renovation Studio",
      description: "Artistic renovation contract for creative spaces and unique transformations",
      category: "creative",
      type: "renovation",
      rating: 4.6,
      downloads: 423,
      preview: "Visually appealing contract design for creative renovation projects",
      features: ["Creative vision protection", "Artistic milestone tracking", "Unique material sourcing", "Design flexibility"],
      color: "bg-purple-50 border-purple-200",
      icon: Palette
    },

    // Legal Standard Templates
    {
      id: "legal-commercial-1",
      name: "Legal Standard Commercial",
      description: "Comprehensive legal contract with maximum protection and industry compliance",
      category: "legal",
      type: "commercial",
      rating: 4.9,
      downloads: 2134,
      preview: "Legally robust contract with comprehensive clauses and industry-standard protections",
      features: ["Maximum legal protection", "Industry compliance", "Risk mitigation", "Dispute resolution"],
      color: "bg-green-50 border-green-200",
      icon: Shield
    },
    {
      id: "legal-maintenance-1",
      name: "Legal Maintenance Agreement",
      description: "Legally sound maintenance contract with ongoing service provisions",
      category: "legal",
      type: "maintenance",
      rating: 4.8,
      downloads: 876,
      preview: "Comprehensive maintenance agreement with legal protections and service standards",
      features: ["Service level agreements", "Warranty provisions", "Emergency response", "Legal compliance"],
      color: "bg-green-50 border-green-200",
      icon: Wrench
    }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory;
    const matchesType = selectedType === "all" || template.type === selectedType;
    
    return matchesSearch && matchesCategory && matchesType;
  });

  const getCategoryBadge = (category: string) => {
    const styles = {
      classic: "bg-slate-100 text-slate-800 border-slate-300",
      modern: "bg-blue-100 text-blue-800 border-blue-300",
      creative: "bg-purple-100 text-purple-800 border-purple-300",
      legal: "bg-green-100 text-green-800 border-green-300"
    };
    return styles[category as keyof typeof styles] || styles.classic;
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      residential: Home,
      commercial: Building,
      renovation: Wrench,
      maintenance: Wrench,
      design: Palette
    };
    return icons[type as keyof typeof icons] || FileText;
  };

  const handlePreviewTemplate = (template: ContractTemplate) => {
    setPreviewTemplate(template);
    setIsPreviewOpen(true);
  };

  const handleUseTemplate = (templateId: string) => {
    // Here you would typically navigate to the contract builder with the selected template
    console.log("Using template:", templateId);
    setIsPreviewOpen(false);
    // You could navigate to the contract builder page with the template pre-loaded
    // navigate(`/contracts?template=${templateId}`);
  };

  return (
    <ContractDashboardLayout>
      <div className="p-4 sm:p-6 lg:p-8">
        {/* Page Header */}
        <div className="mb-6 lg:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Contract Templates</h1>
          <p className="text-muted-foreground mt-2 text-sm sm:text-base">
            Choose from professionally designed contract templates for your construction projects
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="classic">Classic Professional</SelectItem>
                <SelectItem value="modern">Modern Minimalist</SelectItem>
                <SelectItem value="creative">Creative Contemporary</SelectItem>
                <SelectItem value="legal">Legal Standard</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Project Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="residential">Residential</SelectItem>
                <SelectItem value="commercial">Commercial</SelectItem>
                <SelectItem value="renovation">Renovation</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="design">Design-Build</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => {
            const IconComponent = template.icon;
            const TypeIcon = getTypeIcon(template.type);
            
            return (
              <Card key={template.id} className={`${template.color} hover:shadow-lg transition-all duration-300 group`}>
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className={`p-2 rounded-lg ${
                        template.category === 'classic' ? 'bg-slate-200' :
                        template.category === 'modern' ? 'bg-blue-200' :
                        template.category === 'creative' ? 'bg-purple-200' :
                        'bg-green-200'
                      }`}>
                        <IconComponent className="h-5 w-5" />
                      </div>
                      <TypeIcon className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <Badge className={getCategoryBadge(template.category)}>
                      {template.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg group-hover:text-primary transition-colors">
                    {template.name}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {template.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>{template.rating}</span>
                    </div>
                    <span>{template.downloads} downloads</span>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Key Features:</p>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {template.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <div className="w-1 h-1 bg-current rounded-full" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => handlePreviewTemplate(template)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Button
                      size="sm"
                      className="flex-1"
                      onClick={() => handleUseTemplate(template.id)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Use Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-muted-foreground">Try adjusting your search criteria or filters</p>
          </div>
        )}

        {/* Template Preview Modal */}
        <TemplatePreviewModal
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          template={previewTemplate}
          onUseTemplate={handleUseTemplate}
        />
      </div>
    </ContractDashboardLayout>
  );
};

export default ContractTemplates;
